<template>
  <div class="page-wrapper page-pending-edit-ticket">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ ticketDetails.title }}
            </h2>
            <v-chip size="x-small" class="data-count ml-2">
              {{ strings.editTicket.ticketId }} -
              {{ ticketDetails.ticket_id }}
            </v-chip>
          </div>
          <!-- <v-breadcrumbs :items="items" class="custom-breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs> -->
          <!-- <v-breadcrumbs :items="items" class="custom-breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs> -->
        </v-col>
        <v-col cols="auto" class="page-action">
          <div class="btn-toolbar">
            <v-btn variant="outlined" class="btn-outlined-dark" @click="goBack">
              <v-icon left> mdi-chevron-left </v-icon
              >{{ strings.viewUser.buttons.back }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Page Content Section -->
    <section class="page-inner-content page-content-wrapper page-two-col-layout fixed-right-sidebar">
      <div class="page-inner-layout">
        <div class="page-left-column">
          <!-- Ticket Current Info -->
          <div class="ticket-current-info mb-4">
            <v-row align="center" justify="space-evenly">
              <v-col sm="12" lg="auto">
                <label class="ticket-data-label">{{
                  strings.editTicket.currentAssigneeName
                }}</label>
                <div class="ticket-data-value">
                  {{ ticketDetails?.assigned_to_fullname || "--N/A--" }}
                </div>
              </v-col>
              <v-col sm="12" lg="auto">
                <label class="ticket-data-label">{{
                  strings.editTicket.currentStatusName
                }}</label>
                <div class="ticket-data-value">
                  {{ ticketDetails.status_name }}
                </div>
              </v-col>
            </v-row>
          </div>
          <!-- Tab Begin Here -->
          <v-card class="custom-tab-wrapper">
            <v-tabs v-model="tab">
              <v-tab value="tab-ticket-info"> {{strings.pendingEditApproval.pendingEditApprovalTabTitles.ticketInfo}} </v-tab>
              <v-tab value="tab-stats"> {{strings.pendingEditApproval.pendingEditApprovalTabTitles.ticketDescription}} </v-tab>
              <v-tab value="tab-tracking"> {{strings.pendingEditApproval.pendingEditApprovalTabTitles.ticketTracking}} </v-tab>
            </v-tabs>

            <v-card-text>
              <v-tabs-window v-model="tab">
                <v-tabs-window-item value="tab-ticket-info">
                  <div class="tab-content-wrapper">
                    <v-row align="center" justify="space-between">
                      <v-col md="12" lg="auto">
                        <label class="label-name">
                          {{ strings.editTicket.projectName }}
                        </label>
                        <div class="label-value">
                          {{ ticketDetails.project_name }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.location
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.location_name }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.priority
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.priority_names }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.createDate
                        }}</label>
                        <div class="label-value">
                          {{ formatDate(ticketDetails.created_at) }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.updateDate
                        }}</label>
                        <div class="label-value">
                          {{ formatDate(ticketDetails.updated_at) }}
                        </div>
                      </v-col>
                    </v-row>
                    <v-row align="center" justify="space-between" class="mt-4">
                      <v-col lg="12">
                        <label class="label-name"> {{strings.pendingEditApproval.ticketDescriptionLabel.documents}} </label>
                        <div class="mt-1 batch-document-wrapper">
                          <Batch
                            v-for="(doc, index) in ticketDetails.attachement"
                            :key="index"
                            class="batch-documents"
                          >
                            <a :href="doc.attachement" target="_blank">
                              {{ getFileName(doc.attachement) }}
                            </a>
                          </Batch>
                        </div>
                      </v-col>
                    </v-row>
                    <v-row class="mt-4">
                      <v-col>
                        <h6 class="tab-section-title">{{strings.pendingEditApproval.ticketDescriptionLabel.ticketComments}}</h6>
                        <Chatbox />
                      </v-col>
                    </v-row>
                  </div>
                </v-tabs-window-item>

                <v-tabs-window-item value="tab-stats">
                  <div class="tab-content-wrapper">
                    <v-row align="center" justify="space-between">
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{strings.pendingEditApproval.ticketDescriptionLabel.createdBy}}</label>
                        <div class="label-value">
                          {{ ticketDetails.created_by_fullname }}
                        </div>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col
                        v-if="
                          ticketDetails.approvel_message &&
                          ticketDetails.approvel_message.length > 0
                        "
                        md="12"
                        lg="auto"
                      >
                        <label class="label-name">{{strings.pendingEditApproval.ticketDescriptionLabel.approvalMessage}}</label>
                        <div class="label-value">
                          {{ ticketDetails.approvel_message || "N/A" }}
                        </div>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col
                        v-if="ticketDetails?.justification?.trim().length"
                        md="12"
                        lg="auto"
                      >
                        <label class="label-name">{{strings.pendingEditApproval.ticketDescriptionLabel.justification}}</label>
                        <div class="label-value">
                          {{ ticketDetails.justification || "N/A" }}
                        </div>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col>
                        <label class="label-name">{{strings.pendingEditApproval.ticketDescriptionLabel.description}}</label>
                        <div class="label-value">
                          {{ ticketDetails.description }}
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                </v-tabs-window-item>

                <v-tabs-window-item value="tab-tracking">
                  <v-timeline align="start" dense>
                    <v-timeline-item
                      v-for="(item, index) in statusTrackingData"
                      :key="index"
                      :dot-color="getStatusColor(item.current_status || item)"
                      size="small"
                    >
                      <v-card variant="plain">
                        <v-card-title>
                          {{ getStatusText(item.current_status) }}
                        </v-card-title>
                        <v-card-subtitle>
                          {{strings.pendingEditApproval.ticketTrackingLabel.updatedBy}} {{ item.updated_by || "Unknown" }}
                        </v-card-subtitle>
                        <v-card-text class="timeline-card-text mt-2">
                          <div class="d-block">
                            <label class="d-block">{{strings.pendingEditApproval.ticketTrackingLabel.createdBy}}</label>
                            <strong class="d-block">{{
                              item.created_by
                            }}</strong>
                          </div>
                          <div class="d-block mt-2">
                            <label class="d-block">{{strings.pendingEditApproval.ticketTrackingLabel.description}}</label>
                            <p class="d-block text-left">
                              {{ getStatusDescription(item.current_status) }}
                            </p>
                          </div>
                          <div class="d-block mt-2">
                            <label class="d-block">{{strings.pendingEditApproval.ticketTrackingLabel.date}}</label>
                            <strong>{{ formatDate(item.created_at) }}</strong>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-timeline-item>
                  </v-timeline>
                </v-tabs-window-item>
              </v-tabs-window>
            </v-card-text>
          </v-card>
          <!-- Tab End Here -->
        </div>
        <div class="page-right-column">
          <v-card class="edit-ticket-fixed-card">
            <v-card-text>
              <div class="row">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.status"
                      :items="statusOptions"
                      :label="strings.pendingEditApproval.approvalStatus"
                      :disabled="true"
                      variant="outlined"
                      item-title="text"
                      item-value="value"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.status.$touch"
                      @change="v$.status.$touch"
                    />
                  </div>
                  <div class="mt-3">
                    <v-textarea
                      v-model="state.approvel_message"
                      :label="strings.pendingEditApproval.approvalMessage"
                      :disabled="!isAdmin"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.approvel_message.$touch"
                      @change="v$.approvel_message.$touch"
                    />
                  </div>
                  <!-- <div class="mt-3">
                    <v-file-input
                      v-model="state.attachement"
                      :error-messages="
                        v$.attachement.$errors.map((e) => String(e.$message))
                      "
                      :label="strings.pendingEditApproval.uploadDocuments"
                      variant="outlined"
                      :disabled="!isAdmin"
                      dense
                      required
                      item-title="text"
                      item-value="value"
                      multiple
                      show-size
                      accept=".pdf,.doc,.docx,.png,.jpg"
                      class="select-field small-select custom-upload-icon"
                      @blur="v$.attachement.$touch"
                    />
                  </div> -->
                </div>
              </div>
              <div class="btn-toolbar mt-4 justify-center">
                <v-btn
                  variant="text"
                  @click="handleCancel"
                  :disabled="!isAdmin"
                >
                  {{ strings.editTicket.cancelButton }}
                </v-btn>
                <v-btn
                  variant="tonal"
                  :disabled="!isFormModified || isSubmitting"
                  @click="handleSubmit"
                >
                  {{ strings.editTicket.submitButton }}
                  <template #loader>
                    <v-progress-circular
                      indeterminate
                      size="20"
                      class="spinner"
                    />
                  </template>
                </v-btn>
              </div>
            </v-card-text>
          </v-card>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref } from "vue";
import { useVuelidate } from "@vuelidate/core";
import { email, helpers, required } from "@vuelidate/validators";
import Button from "../../components/Button.vue";
import Batch from "../../components/Batch.vue";
import {
  getTicketById,
  apiClient,
  getStatus,
  getStatusTracking,
} from "../../api/apiClient";
import { useRoute, useRouter } from "vue-router";
import { useTicketStore } from "../../stores/piniaStore";
import strings from "../../assets/strings.json";
import { useToast } from "vue-toastification";

interface State {
  approved_by: string | null;
  status: any;
  attachement: File[] | null;
  approvel_message: string | null;
}

interface Ticket {
  id: number;
  current_status: string;
  updated_by: string;
  created_by: string;
  created_at: string;
}

export default defineComponent({
  name: "PendingEditTicket",
  components: {
    Button,
    Batch,
  },
  setup() {
    // Reactive state for form
    const state = reactive<State>({
      status: null,
      attachement: null,
      approvel_message: null,
      approved_by: null,
    });

    // Route to get ticket ID
    const route = useRoute();
    const router = useRouter();
    const toast = useToast();
    const ticketId = Number((route.params as { ticket_id: string }).ticket_id);
    // const ticketStore = useTicketStore();
    // const statusOptions = ticketStore.statusOptions;
    const statusOptions = ref<any[]>([]);
    const statusMasterMap = ref<any>({});
    const statusTrackingData = ref<any>([]);
    const maxFileSize = 2 * 1024 * 1024;
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const isSubmitting = ref(false);

    const isAdmin = computed(() => user.id === ticketDetails.value.assigned_to);

    // Ticket details
    const ticketDetails = ref<Record<string, any>>({
      ticket_id: "",
      attachement: [],
      title: "",
      project: "",
      type: "",
      description: "",
      urgency: "",
      watchers: "",
      created_at: "",
      updated_at: "",
      solved_at: "",
      closed_at: "",
      approvel_status: "",
      justification: "",
      category: "",
      subcategory: "",
      location: "",
      created_by: "",
      assigned_to: "",
      status: "",
      approved_by: "",
    });

    const goBack = () => {
      router.back();
    };

    // Validation rules
    const rules = {
      status: { required },
      approvel_message: { required },
      attachement: {
        fileSize: helpers.withMessage(
          "Each file must be 2MB or less",
          (value: File[] | null) =>
            !value ||
            (Array.isArray(value) &&
              value.every((file) => file.size <= maxFileSize))
        ),
      },
    };

    const isFormModified = computed(() => {
      return (
        state.approvel_message !== initialState.approvel_message ||
        JSON.stringify(state.attachement) !==
          JSON.stringify(initialState.attachement)
      );
    });

    const v$ = useVuelidate(rules, state);

    // Initial state
    const initialState: State = {
      status: null,
      attachement: null,
      approvel_message: null,
      approved_by: null,
    };
    // Function to reset the form
    const clear = (): void => {
      v$.value.$reset();
      Object.assign(state, initialState);
    };

    const handleCancel = () => {
      // Reset state back to initial values
      Object.assign(state, JSON.parse(JSON.stringify(initialState)));
    };

    // Function to handle form submission
    const handleSubmit = async (): Promise<void> => {
      if (isSubmitting.value) return; // Prevent multiple submissions
      isSubmitting.value = true;

      v$.value.$validate(); // Validate the form

      if (!v$.value.$error) {
        try {
          const apiUrl = `tickets/${ticketDetails.value.ticket_id}/`;
          const formData = new FormData();
          state.status = 1;

          // Append data to FormData
          formData.append("approvel_status", state.status || "");
          formData.append("message", state.approvel_message || "");
          formData.append("approved_by", state.approved_by || "");

          if (state.attachement) {
            state.attachement.forEach((file) => {
              formData.append("attachement", file);
            });
          }
          const payload = {
            ...state,
            title: ticketDetails.value.title,
            description: ticketDetails.value.description,
            project: ticketDetails.value.project,
            ticket_id: ticketDetails.value.ticket_id,
            approved_by_id: ticketDetails.value.created_by,
            assigned_to: null,
            // updated_by: user.id,
          };
          const response = await apiClient.put(apiUrl, payload);
          toast.success("Form updated successfully!");
          router.push("/pending-approval");
          clear();
        } catch (error: any) {
          console.error("Error submitting form:", error);

          // Check if the error has a response from the server
          if (
            error.response &&
            error.response.data &&
            error.response.data.message
          ) {
            toast.error(`Error: ${error.response.data.message}`);
          } else {
            toast.error(`Error submitting form: ${error.message}`);
          }
        } finally {
          isSubmitting.value = false;
        }
      } else {
        console.error("Validation Errors:", v$.value.$errors);
        toast.warning("Please correct the errors before submitting.");
        isSubmitting.value = false;
      }
    };

    const fetchStatusTracking = async () => {
      try {
        const statusTracking = await getStatusTracking({ id: ticketId });

        // Sort data in descending order by created_at
        statusTrackingData.value = statusTracking.data.sort(
          (a: any, b: any) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      } catch (error: any) {
        console.error("Error fetching ticket data:", error);
      }
    };

    const fetchStatusMaster = async () => {
      try {
        const response = await getStatus(); // Fetch status master data
        const statusList = response;

        // Convert status master into a dictionary (id -> { statusName, description })
        statusMasterMap.value = statusList.reduce((acc: any, status: any) => {
          acc[status.id] = {
            name: status.name,
            description: status.description,
          };
          return acc;
        }, {});
      } catch (error: any) {
        console.error("Error fetching status master data:", error);
      }
    };

    // Get status text from status master table
    const getStatusText = (statusId: any) => {
      return statusMasterMap.value[statusId]?.name || "Unknown Status";
    };

    // Get status description from status master table
    const getStatusDescription = (statusId: any) => {
      return (
        statusMasterMap.value[statusId]?.description ||
        "No description available"
      );
    };

    // Map status IDs to colors
    const getStatusColor = (statusId: number): string => {
      const colors: Record<number, string> = {
        1: "#1976D2", // Open - Blue
        2: "#FF9800", // In Progress - Orange
        3: "#00BCD4", // New - Cyan
        4: "#9C27B0", // Under Observer - Purple
        5: "#FFC107", // Pending - Yellow
        6: "#4CAF50", // Solved - Green
        7: "#F44336", // Closed - Red
        8: "#E91E63", // Awaiting Approval - Pink
        9: "#009688", // Awaiting User Response - Teal
        10: "#795548", // Cancel - Brown
        11: "#3F51B5", // Awaiting Payment - Indigo
        12: "#FF5722", // Requests For Deployment - Deep Orange
      };

      return colors[statusId] || "#9E9E9E"; // Default Gray if not found
    };

    const formatDate = (dateString: any) => {
      return new Date(dateString).toLocaleString("en-US", {
        month: "short", // Apr
        day: "2-digit", // 29
        year: "numeric", // 2025
        hour: "2-digit", // 06
        minute: "2-digit", // 46
        hour12: true, // PM/AM
      });
    };

    // Fetch ticket details on mount
    onMounted(async () => {
      await fetchStatusMaster();
      await fetchStatusTracking();
      try {
        const statusData = await getStatus();
        statusOptions.value = statusData.map(
          (status: { id: number; name: string }) => ({
            value: status.id,
            text: status.name,
          })
        );
        const ticketData = await getTicketById({ id: ticketId });
        ticketDetails.value = {
          ...ticketDetails.value,
          ...ticketData,
        };
        // Populate state with ticket data for editing
        state.status = ticketData.status ?? null;
        state.approvel_message = ticketData.approvel_message || "";
        state.approved_by = ticketData.approved_by;
        state.attachement = null;
        Object.assign(initialState, JSON.parse(JSON.stringify(state)));
      } catch (error: any) {
        console.error("Error fetching ticket data:", error);
        toast.warning(
          "Failed to load ticket details. Please refresh the page."
        );
      }
    });
    const tab = ref("tab-ticket-info");

    return {
      state,
      v$,
      handleSubmit,
      clear,
      ticketDetails,
      statusOptions,
      statusTrackingData,
      strings,
      getStatusText,
      getStatusDescription,
      getStatusColor,
      tab,
      isFormModified,
      handleCancel,
      goBack,
      formatDate,
      isAdmin,
      isSubmitting
    };
  },
  data() {
    return {
      ticketList: [] as Ticket[],
      items: [
        { title: "Home", disabled: false, href: "/" },
        {
          title: "Ticket Management",
          disabled: false,
          href: "/all-tickets",
        },
        {
          title: "Pending Ticket",
          disabled: false,
          href: "/pending-approval",
        },
        {
          title: "Edit Tickets",
          disabled: true,
          href: "",
        },
      ],
    };
  },
  methods: {
    // formatDate(dateString: string): string {
    //   return new Date(dateString).toLocaleDateString("en-US", {
    //     year: "numeric",
    //     month: "long",
    //     day: "numeric",
    //   });
    // },
    getFileName(url: string): string {
      return url.split("/").pop() || "";
    },
  },
});
</script>

<style scoped>
.right-col {
  background: #fff;
  border-left: 1px solid #d8d8d8;
  /* height: 100vh; */
}
.right-col span {
  color: #4a4a4a;
  font-weight: 500;
}
.left-col {
  background: #fff;
  border-right: 1px solid #d8d8d8;
  /* height: 100vh; */
}
.select-field {
  cursor: pointer;
  /* width: 307px !important; */
  max-width: 200px !important;
  padding: 0 !important;
  margin-top: 4px;
}
.card-info {
  background: #026bb1;
  border-radius: 5px;
}
.card-info span {
  color: #7eb9e0;
  font-weight: 500;
  font-size: 14px;
}
.card-info p {
  color: #fff;
  font-weight: 500;
  font-size: 14px;
}
.card-2 {
  border: 1px solid #d8d8d8;
  border-radius: 5px;
}
.small-select {
  max-width: 100px; /* Adjust width as needed */
}
.card-2 span {
  color: #8c8c8c;
  font-size: 14px;
}
.card-2 p {
  color: #2e2e2e;
  font-size: 14px;
  font-weight: 500;
}
.titles {
  color: #2e2e2e;
  font-size: 23px;
  font-weight: 600;
  padding: 10px 0;
}
</style>
