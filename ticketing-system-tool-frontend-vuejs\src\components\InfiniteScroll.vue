<template>
  <v-container ref="scrollContainer" class="scroll-container">
    <slot :items="items" :loading="isLoading" :error-message="errorMessage" />

    <!-- Loading Indicator -->
    <v-progress-circular
      v-if="isLoading"
      indeterminate
      color="primary"
      class="d-block mx-auto mt-3"
    />

    <!-- No More Data Message -->
    <v-alert v-if="!isLoading && !hasMore" type="info" class="mt-3">
      No more items to load.
    </v-alert>

    <!-- Error Message -->
    <v-alert v-if="errorMessage" type="error" class="mt-3">
      {{ errorMessage }}
    </v-alert>
  </v-container>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, nextTick } from "vue";
import axios from "axios";

export default defineComponent({
  props: {
    apiUrl: { type: String, required: true }, // API endpoint
    pageSize: { type: Number, default: 10 }, // Default page size
  },
  emits: ["update:data"], // Emit event when data updates
  setup(props, { emit }) {
    const items = ref<any[]>([]);
    const currentPage = ref<number>(1);
    const isLoading = ref<boolean>(false);
    const hasMore = ref<boolean>(true);
    const errorMessage = ref<string | null>(null);

    // Function to load more data
    const loadMore = async () => {
      if (!hasMore.value || isLoading.value) return;
      isLoading.value = true;
      errorMessage.value = null;

      try {
        const response = await axios.get(
          `${props.apiUrl}?page=${currentPage.value}&page_size=${props.pageSize}`
        );
        console.log(response);

        if (response.data.results && response.data.results.length > 0) {
          items.value = [...items.value, ...response.data.results];
          emit("update:data", items.value); // Emit updated data
          currentPage.value += 1;
        } else {
          hasMore.value = false;
        }
      } catch (error: any) {
        errorMessage.value = "Error fetching data. Please try again later.";
        console.error("Error fetching data:", error);
      } finally {
        isLoading.value = false;
      }
    };

    // Scroll detection function
    const handleScroll = () => {
      if (
        window.innerHeight + window.scrollY >=
        document.body.offsetHeight - 100
      ) {
        loadMore();
      }
    };

    onMounted(() => {
      nextTick(() => {
        window.addEventListener("scroll", handleScroll);
      });
      loadMore();
    });

    onUnmounted(() => {
      window.removeEventListener("scroll", handleScroll);
    });

    return {
      items,
      isLoading,
      hasMore,
      errorMessage,
      loadMore,
    };
  },
});
</script>

<style scoped>
.scroll-container {
  min-height: 500px;
  overflow-y: auto;
  border: 1px solid #ddd;
  padding: 10px;
}
</style>
