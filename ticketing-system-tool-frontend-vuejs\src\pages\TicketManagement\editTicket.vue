<template>
  <div class="page-wrapper page-edit-ticket">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ ticketDetails.title }}
            </h2>
            <v-chip size="x-small" class="data-count ml-2">
              {{ strings.editTicket.ticketId }} -
              {{ ticketDetails.ticket_id }}
            </v-chip>
          </div>
          <!-- <v-breadcrumbs
            :items="items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs> -->
        </v-col>
        <v-col cols="auto" class="page-action">
          <div class="btn-toolbar">
            <v-btn variant="outlined" class="btn-outlined-dark" @click="goBack">
              <v-icon left> mdi-chevron-left </v-icon
              >{{ strings.viewUser.buttons.back }}
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </div>

    <!-- Page Content Section -->
    <section
      class="page-inner-content page-content-wrapper page-two-col-layout fixed-right-sidebar"
    >
      <div class="page-inner-layout">
        <div class="page-left-column">
          <!-- Ticket Current Info -->
          <div class="ticket-current-info mb-4">
            <v-row align="center" justify="space-evenly">
              <v-col sm="12" lg="auto">
                <label class="ticket-data-label">{{
                  strings.editTicket.currentAssigneeName
                }}</label>
                <div class="ticket-data-value">
                  {{ ticketDetails?.assigned_to_fullname || "--N/A--" }}
                </div>
              </v-col>
              <v-col sm="12" lg="auto">
                <label class="ticket-data-label">{{
                  strings.editTicket.currentStatusName
                }}</label>
                <div class="ticket-data-value">
                  {{ ticketDetails.status_name }}
                </div>
              </v-col>
            </v-row>
          </div>

          <!-- Tab Begin Here -->
          <v-card class="custom-tab-wrapper">
            <v-tabs v-model="tab">
              <v-tab value="tab-ticket-info">
                {{ strings.editTicket.editTicketTabTitles.ticketInfo }}
              </v-tab>
              <v-tab value="tab-stats">
                {{ strings.editTicket.editTicketTabTitles.ticketDescription }}
              </v-tab>
              <v-tab value="tab-tracking">
                {{ strings.editTicket.editTicketTabTitles.ticketTracking }}
              </v-tab>
            </v-tabs>

            <v-card-text>
              <v-tabs-window v-model="tab">
                <v-tabs-window-item value="tab-ticket-info">
                  <div class="tab-content-wrapper">
                    <v-row align="center" justify="space-between">
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.projectName
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.project_name }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.location
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.location_name }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.priority
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.priority_names }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.createDate
                        }}</label>
                        <div class="label-value">
                          {{ formatDate(ticketDetails.created_at) }}
                        </div>
                      </v-col>
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.updateDate
                        }}</label>
                        <div class="label-value">
                          {{ formatDate(ticketDetails.updated_at) }}
                        </div>
                      </v-col>
                    </v-row>
                    <v-row align="center" justify="space-between" class="mt-4">
                      <v-col lg="12">
                        <label class="label-name">{{
                          strings.editTicket.ticketDescriptionLabel.documents
                        }}</label>
                        <div class="mt-1 batch-document-wrapper">
                          <AttachmentDownload
                            :attachments="ticketDetails.attachement"
                            :multiple="true"
                            :title="
                              strings.editTicket.ticketDescriptionLabel
                                .documents
                            "
                            @download-start="onDownloadStart"
                            @download-complete="onDownloadComplete"
                            @download-error="onDownloadError"
                          />
                        </div>
                      </v-col>
                    </v-row>
                    <v-row class="mt-4">
                      <v-col>
                        <h6 class="tab-section-title">
                          {{
                            strings.editTicket.ticketDescriptionLabel
                              .ticketComments
                          }}
                        </h6>
                        <Chatbox />
                      </v-col>
                    </v-row>
                  </div>
                </v-tabs-window-item>

                <v-tabs-window-item value="tab-stats">
                  <div class="tab-content-wrapper">
                    <v-row align="center" justify="space-between">
                      <v-col
                        md="12"
                        lg="auto"
                        class="col-12 d-flex justify-content-between"
                      >
                        <div>
                          <label class="label-name">{{
                            strings.editTicket.ticketDescriptionLabel.createdBy
                          }}</label>
                          <div class="label-value">
                            {{ ticketDetails.created_by_fullname }}
                          </div>
                        </div>
                        <div>
                          <label class="label-name">{{
                            strings.editTicket.createDate
                          }}</label>
                          <div class="label-value">
                            {{ formatDate(ticketDetails.created_at) }}
                          </div>
                        </div>
                      </v-col>
                      <!-- <v-col md="12" lg="auto">
                        <label class="label-name">Approved By</label>
                        <div class="label-value">
                          {{ ticketDetails.approved_by || "N/A" }}
                        </div>
                      </v-col> -->
                      <v-col md="12" lg="auto">
                        <label class="label-name">{{
                          strings.editTicket.ticketDescriptionLabel.description
                        }}</label>
                        <div class="label-value description-text">
                          {{ ticketDetails.description }}
                        </div>
                      </v-col>

                      <v-col
                        v-if="ticketDetails?.justification?.trim().length"
                        md="12"
                        lg="auto"
                      >
                        <label class="label-name">{{
                          strings.editTicket.ticketDescriptionLabel
                            .justification
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.justification || "N/A" }}
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          ticketDetails.approvel_message &&
                          ticketDetails.approvel_message.length > 0
                        "
                        md="12"
                        lg="auto"
                      >
                        <label class="label-name">{{
                          strings.editTicket.ticketDescriptionLabel
                            .approvalMessage
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.approvel_message || "N/A" }}
                        </div>
                      </v-col>
                      <v-col
                        v-if="ticketDetails?.approved_by_name?.trim().length"
                        md="12"
                        lg="auto"
                      >
                        <label class="label-name">{{
                          strings.editTicket.ticketDescriptionLabel.approvedBy
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.approved_by_name || "N/A" }}
                        </div>
                      </v-col>
                      <v-col
                        v-if="
                          ticketDetails.cancel_message &&
                          ticketDetails.cancel_message.length > 0
                        "
                        md="12"
                        lg="auto"
                      >
                        <label class="label-name">{{
                          strings.editTicket.ticketDescriptionLabel.cancelReason
                        }}</label>
                        <div class="label-value">
                          {{ ticketDetails.cancel_message || "N/A" }}
                        </div>
                      </v-col>
                    </v-row>
                  </div>
                </v-tabs-window-item>

                <v-tabs-window-item value="tab-tracking">
                  <v-timeline align="start" dense>
                    <v-timeline-item
                      v-for="(item, index) in statusOptions"
                      :key="index"
                      :dot-color="getStatusColor(item.current_status || item)"
                      size="small"
                    >
                      <v-card variant="plain">
                        <v-card-title>
                          {{ getStatusText(item.current_status) }}
                        </v-card-title>
                        <v-card-subtitle>
                          {{ strings.editTicket.ticketTrackingLabel.updatedBy }}
                          {{ item.updated_by || "Unknown" }}
                        </v-card-subtitle>
                        <v-card-text class="timeline-card-text mt-2">
                          <div class="d-block">
                            <label class="d-block">{{
                              strings.editTicket.ticketTrackingLabel.createdBy
                            }}</label>
                            <strong class="d-block">{{
                              item.created_by
                            }}</strong>
                          </div>
                          <div class="d-block mt-2">
                            <label class="d-block">{{
                              strings.editTicket.ticketTrackingLabel.description
                            }}</label>
                            <p class="d-block text-left">
                              {{ getStatusDescription(item.current_status) }}
                            </p>
                          </div>
                          <div class="d-block mt-2">
                            <label class="d-block">{{
                              strings.editTicket.ticketTrackingLabel.date
                            }}</label>
                            <strong>{{ formatDate(item.created_at) }}</strong>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-timeline-item>
                  </v-timeline>
                </v-tabs-window-item>
              </v-tabs-window>
            </v-card-text>
          </v-card>
          <!-- Tab End Here -->
        </div>
        <div class="page-right-column">
          <v-card class="edit-ticket-fixed-card">
            <v-card-text>
              <div class="row">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.category"
                      :disabled="isReadonly"
                      :error-messages="
                        v$.category.$errors.map((e) => String(e.$message))
                      "
                      :items="categories"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.category"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.category.$touch"
                      @change="v$.category.$touch"
                      @input="v$.category.$touch"
                      @update:model-value="updateSubcategories"
                    />
                  </div>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.subcategory"
                      :disabled="isReadonly"
                      :error-messages="
                        v$.subcategory.$errors.map((e) => String(e.$message))
                      "
                      :items="subcategories"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.subcategory"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.subcategory.$touch"
                      @change="v$.subcategory.$touch"
                      @input="v$.subcategory.$touch"
                    />
                  </div>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-12">
                  <div class="custom-select">
                    <v-select
                      v-model="state.status"
                      :disabled="isReadonly"
                      :v-error-messages="
                        v$.status.$errors.map((e) => String(e.$message))
                      "
                      :items="status"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.status"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                      @blur="v$.status.$touch"
                      @change="v$.status.$touch"
                    >
                      <template #selection="{ item }">
                        <span v-if="item && (item.raw as any)?.text">
                          {{ (item.raw as any).text }}
                        </span>
                        <span v-else>
                          {{ getStatusName(Number(state.status) || 0) }}
                        </span>
                      </template>
                    </v-select>
                  </div>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-12">
                  <div class="custom-select">
                    <v-autocomplete
                      v-model="state.assigned_to"
                      :disabled="isReadonly"
                      :error-messages="
                        v$.assigned_to.$errors.map((e) => String(e.$message))
                      "
                      :items="assigned_to"
                      item-title="text"
                      item-value="value"
                      :label="strings.editTicket.assignee"
                      variant="outlined"
                      density="compact"
                      required
                      :searchable="true"
                      class="select-field small-select"
                      @blur="v$.assigned_to.$touch"
                      @change="v$.assigned_to.$touch"
                      @input="v$.assigned_to.$touch"
                    />
                  </div>
                </div>
              </div>
              <div class="row mt-4" v-if="isAdmin">
                <div class="col-12">
                  <v-text-field
                    id="due-date"
                    v-model="state.due_date"
                    :disabled="isReadonly"
                    type="datetime-local"
                    :min="minDateTime"
                    :v-error-messages="
                      v$.due_date.$errors.map((e) => String(e.$message))
                    "
                    :items="state.due_date"
                    variant="outlined"
                    dense
                    required
                    label="Due Date"
                    class="select-field small-select custom-due-date-field"
                    @blur="v$.due_date.$touch"
                    @change="handleDueDateChange"
                  />
                  <!-- <div class="custom-form-field-wrapper">
                    <div for="due-date">Due Date</div>

                    <v-text-field
  v-model="state.due_date"
  :disabled="isEmployee"
  type="datetime-local"
  label="Due Date"
  variant="outlined"
  dense
  class="select-field small-select"
/>
                  </div> -->
                </div>
              </div>
              <div class="row mt-5">
                <div class="col-12">
                  <div v-if="showExtensionField">
                    <v-textarea
                      id="due-date-extension"
                      :error-messages="
                        v$.due_expiry_reason.$errors.map((e) =>
                          String(e.$message)
                        )
                      "
                      v-model="state.due_expiry_reason"
                      :readonly="readonlyDueExtension"
                      :disabled="isReadonly"
                      :label="strings.editTicket.dueDateExtensionLabel"
                      variant="outlined"
                      dense
                      required
                      class="select-field small-select"
                    />
                  </div>
                </div>
              </div>
              <div class="btn-toolbar mt-4 justify-center">
                <!-- If the user is an Employee -->
                <template
                  v-if="ticketDetails.status === 6 && isCreatedByLoggedInUser"
                >
                  <!-- Show 'Closed' button if the ticket is 'solved' -->
                  <v-btn variant="tonal" @click="handleClosed">
                    {{ strings.common.closeButton }}
                  </v-btn>

                  <!-- Show 'Reopen' button if the ticket is 'closed' -->
                  <v-btn variant="tonal" @click="handleReopen">
                    {{ strings.common.reopenButton }}
                  </v-btn>
                </template>

                <!-- If the user is NOT an Employee -->
                <template v-else>
                  <v-btn
                    v-if="!isReadonly"
                    variant="text"
                    @click="handleCancel"
                  >
                    {{ strings.editTicket.cancelButton }}
                  </v-btn>
                  <v-btn
                    v-if="!loading && !isReadonly"
                    variant="tonal"
                    :disabled="!isFormModified"
                    :style="
                      loading
                        ? { backgroundColor: '#2dcc70', color: '#fff' }
                        : {}
                    "
                    @click="handleSubmit"
                  >
                    {{ strings.editTicket.submitButton }}
                    <template #loader>
                      <v-progress-circular
                        indeterminate
                        size="20"
                        class="spinner"
                      />
                    </template>
                  </v-btn>
                </template>
              </div>
            </v-card-text>
          </v-card>
          <template>
            <v-dialog v-model="showFeedbackDialog" max-width="500px">
              <v-card class="feedback-card">
                <!-- Header -->
                <v-card-title class="feedback-header">
                  <span>{{ strings.feedback.title }}</span>
                </v-card-title>

                <!-- Description -->
                <v-card-text class="feedback-text">
                  <p>
                    {{ strings.feedback.description }}
                  </p>
                </v-card-text>

                <!-- Emoji Rating System -->
                <v-container class="emoji-container">
                  <v-row class="justify-center">
                    <v-col
                      v-for="(feedback, index) in feedbackValues"
                      :key="index"
                      cols="auto"
                      class="emoji-col"
                    >
                      <v-btn
                        class="emoji-btn"
                        :class="{
                          selected: selectedFeedback === feedback.value_id,
                        }"
                        @click="selectFeedback(feedback)"
                      >
                        <span class="emoji">{{ feedback.emoji }}</span>
                      </v-btn>
                      <p class="emoji-label">
                        {{ feedback.label }}
                      </p>
                    </v-col>
                  </v-row>
                  <v-form ref="feedbackForm" v-model="feedbackFormIsValid">
                    <v-row>
                      <v-col>
                        <v-textarea
                          v-model="feedbackState.feedbackReason"
                          label="Reason"
                          variant="outlined"
                          :rules="feedbackRules"
                          dense
                          required
                          class="mb-4 my-textarea"
                        />
                      </v-col>
                    </v-row>
                  </v-form>
                </v-container>

                <!-- Submit Button -->
                <v-container class="submit-container">
                  <v-btn class="submit-button" @click="submitFeedback">
                    {{ strings.feedback.submitButton }}
                  </v-btn>
                </v-container>
              </v-card>
            </v-dialog>
          </template>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, computed } from "vue";
import { useVuelidate } from "@vuelidate/core";
import { minLength, required, helpers } from "@vuelidate/validators";
import { useToast } from "vue-toastification";
import router from "../../router";
import Button from "../../components/Button.vue";
import Batch from "../../components/Batch.vue";
import AttachmentDownload from "../../components/AttachmentDownload.vue";
import {
  getTicketByChat,
  getTicketById,
  apiClient,
  ticketStatus,
  getStatus,
  users,
  getStatusTracking,
  getFeedbackValues,
  createFeedback,
  getAllCategories,
} from "../../api/apiClient";
import { useRoute } from "vue-router";
import strings from "../../../src/assets/strings.json";
import Chatbox from "../../components/Chatbox.vue";
import { now } from "lodash-es";

interface State {
  category: any;
  subcategory: any;
  assigned_to: string | null;
  priority: string | null;
  status: string | "";
  due_date: string | null;
  due_expiry_reason: string | null;
}

interface Feedback {
  value_id: number;
  emoji: string;
  label: string;
}

export default defineComponent({
  name: "EditTicket",
  components: {
    Button,
    Batch,
    Chatbox,
    AttachmentDownload,
  },
  setup() {
    const toast = useToast();
    const categories = ref<any[]>([]);
    const subcategories = ref<any[]>([]);
    const statuses = ref<any>([]); // Holds statuses from API
    const status = ref<any>([]);
    const priority = ref<any>([]);
    const assigned_to = ref<any>([]);
    const loading = ref<boolean>(false);
    const showTable = ref(false);
    const route = useRoute();
    const isFromAssignTicket = computed(
      () => route.query.source === "assign-ticket"
    );
    const currentUser = ref({ id: 0 });
    const showFeedbackDialog = ref(false);
    const feedbackValues = ref<Feedback[]>([]);
    const selectedFeedback = ref<number | null>(null);
    const minDateTime = ref("");
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const showExtensionField = ref(false);
    const due_date = ref<string | null>(null);
    const isEmployee = computed(() =>
      ["R003", "R005", "R004"].includes(user.role)
    );
    const isDueDateModified = ref(false); // Track if the user modified the due date
    const previousValidDueDate = ref<string | null>(null);
    const originalDueDate = ref<string | null>(null);
    const feedbackState = reactive({
      feedbackReason: "",
    });
    const isAdmin = computed(() =>
      ["R001", "R002", "R006"].includes(user.role)
    );
    const readonlyDueExtension = ref(true);
    const isReadonly = computed(() => {
      return (
        isEmployee.value ||
        (ticketDetails.value.status === 8 &&
          ticketDetails.value.assigned_to !== user.id) ||
        ticketDetails.value.status === 7
      );
    });

    const comments = ref<Comment[]>([]);

    const isCreatedByLoggedInUser = computed(() => {
      return ticketDetails.value.created_by.id === user.id;
    });
    const feedbackFormIsValid = ref(true);
    const feedbackForm = ref();

    const feedbackRules = computed(() => {
      const requiredFor = [1, 2];

      return [
        (value: string) => {
          if (requiredFor.includes(selectedFeedback.value || 0)) {
            return (
              (!!value && value.trim().length > 0) || "*Reason is required."
            );
          }
          return true;
        },
        (value: string) => {
          if (requiredFor.includes(selectedFeedback.value || 0)) {
            return (
              value?.length >= 100 || "*Reason must be at least 100 characters."
            );
          }
          return true;
        },
      ];
    });

    const handleCancel = () => {
      // Reset form state to initial state
      Object.assign(state, JSON.parse(JSON.stringify(initialState)));
      // Explicitly reset the due date to the initial due date
      state.due_date = initialState.due_date;
      // Hide the Due Date Extension field
      if (
        ticketDetails.value.due_expiry_reason &&
        ticketDetails.value.due_expiry_reason.trim() !== ""
      ) {
        showExtensionField.value = true;
        readonlyDueExtension.value = true;
      } else {
        showExtensionField.value = false;
        readonlyDueExtension.value = false;
        state.due_expiry_reason = null; // Also clear the field if not needed
      }

      // Ensure subcategory resets correctly (convert ID to full object)
      if (state.category) {
        const selectedCategory = categories.value.find(
          (cat) => cat.value === state.category
        );
        if (selectedCategory) {
          subcategories.value = selectedCategory.subcategories;
          const matchedSubcategory = subcategories.value.find(
            (sub) => sub.value === state.subcategory
          );
          state.subcategory = matchedSubcategory
            ? matchedSubcategory.value
            : null;
        }
      } else {
        state.subcategory = null;
      }
    };

    const canReopen = computed(() => {
      return (
        ticketDetails.value.status === 6 &&
        ticketDetails.value.created_by === user?.id
      );
    });
    const goBack = () => {
      router.go(-1); // Go to the previous page in the browser's history
    };

    const isFormModified = computed(() => {
      return (
        state.category !== initialState.category ||
        state.subcategory !== initialState.subcategory ||
        state.assigned_to !== initialState.assigned_to ||
        state.priority !== initialState.priority ||
        state.status !== initialState.status ||
        state.due_date !== initialState.due_date ||
        state.due_expiry_reason !== initialState.due_expiry_reason
      );
    });

    // Updated parseLocalDateTime and handleDueDateChange to preserve previous value if validation fails

    function parseLocalDateTime(dateTimeString: string): Date {
      const [datePart, timePart] = dateTimeString.split("T");
      const [year, month, day] = datePart.split("-").map(Number);
      const [hourStr, minuteStr] = timePart.split(":");

      const hour = parseInt(hourStr);
      const minute = parseInt(minuteStr);

      // Interpret as local datetime (no timezone shift)
      return new Date(year, month - 1, day, hour, minute);
    }

    const handleDueDateChange = () => {
      const currentInput = state.due_date;
      if (!currentInput) return;

      const selectedDateTime = parseLocalDateTime(currentInput);
      const now = new Date();
      const nowFormatted = `${now.getFullYear()}-${String(
        now.getMonth() + 1
      ).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")}T${String(
        now.getHours()
      ).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}:00`;

      console.log("Now1:", nowFormatted);
      const localFormatted = `${selectedDateTime.getFullYear()}-${String(
        selectedDateTime.getMonth() + 1
      ).padStart(2, "0")}-${String(selectedDateTime.getDate()).padStart(
        2,
        "0"
      )}T${String(selectedDateTime.getHours()).padStart(2, "0")}:${String(
        selectedDateTime.getMinutes()
      ).padStart(2, "0")}:00`;
      const existingDueDate = ticketDetails.value.due_date;
      console.log("Selected Date and Time:", localFormatted);
      console.log("Now:", ticketDetails.value.due_date);
      console.log("Now1:", nowFormatted);

      previousValidDueDate.value = currentInput;

      if (
        localFormatted <= ticketDetails.value.due_date ||
        localFormatted <= nowFormatted
      ) {
        toast.error("Please select a future date and time.");
        state.due_date = previousValidDueDate.value;
        showExtensionField.value = false;
        state.due_expiry_reason = null;
        readonlyDueExtension.value = true;
        return;
      } else if (!originalDueDate.value) {
        showExtensionField.value = false;
        state.due_expiry_reason = null;
      } else {
        showExtensionField.value = true;
        readonlyDueExtension.value = false;
      }

      // if (existingDueDate && localFormatted > existingDueDate || localFormatted > nowFormatted) {
      //   showExtensionField.value = true;
      // } else {
      //   showExtensionField.value = false;
      //   state.due_expiry_reason = null;
      // }

      // showExtensionField.value = !!(
      //   existingDueDate &&
      //   selectedDateTime.getTime() !== existingDueDate.getTime()
      // );
    };

    const getMinDateTime = () => {
      const now = new Date();
      const localNow = new Date(
        now.getTime() - now.getTimezoneOffset() * 60000
      );
      return localNow.toISOString().slice(0, 16);
    };

    const fetchFeedbackValues = async () => {
      try {
        const response = await getFeedbackValues();

        // Ensure that response is an array of Feedback objects
        feedbackValues.value = response.map((feedback: any) => ({
          value_id: feedback.value_id,
          emoji: feedback.emoji,
          label: feedback.label,
        }));
      } catch (error: any) {
        console.error("Error fetching feedback values:", error);
      }
    };

    const selectFeedback = (feedback: { value_id: number }) => {
      selectedFeedback.value = feedback.value_id;
      if (feedbackForm.value) {
        feedbackForm.value.validate();
      }
    };

    // Compute selected label
    const selectedLabel = computed(() => {
      const selected = feedbackValues.value.find(
        (feedback) => feedback.value_id === selectedFeedback.value
      );
      return selected ? selected.label : "";
    });

    // Submit feedback
    const submitFeedback = async () => {
      if (feedbackForm.value) {
        const validationResult = await feedbackForm.value.validate();

        if (!validationResult.valid) {
          toast.error("Please provide Reason.");
          return;
        }
      }

      if (!selectedFeedback.value) {
        toast.error("Please select a feedback option before submitting.");
        return;
      }

      try {
        const payload = {
          ticket: ticketId, // Ticket ID from ticketDetails
          user: user.id, // Assuming 'assigned_to' is the user ID
          feedback_value: selectedFeedback.value, // Selected feedback value ID
          reason: feedbackState.feedbackReason,
        };

        const response = await createFeedback(payload); // Call API
        toast.success("Feedback submitted successfully!");
        handleSubmit();
        showFeedbackDialog.value = false; // Close dialog on success
      } catch (error: any) {
        toast.error("Failed to submit feedback. Please try again.");
        console.error("Error submitting feedback:", error);
      }
    };

    const handleClosed = async () => {
      state.status = "7";

      v$.value.$touch();
      await v$.value.$validate();

      if (v$.value.$error) {
        toast.warning("Please correct the errors before closing the ticket.");
        return;
      }

      showFeedbackDialog.value = true;
    };

    const handleReopen = async () => {
      try {
        // Ensure status is updated before calling handleSubmit
        state.status = "1"; // Set status to 'Open' (ID 1)

        // Call handleSubmit() only after ensuring the state update
        await handleSubmit();
      } catch (error: any) {
        console.error("Error reopening ticket:", error);
      }
    };

    // Computed property to map `assigned_to` to the corresponding status name
    const assignedToStatusName = computed(() => {
      const status = statuses.value.find(
        (s: any) => s.id === ticketDetails.value.status
      );

      return status ? status.name : "--N/A--";
    });

    // Fetch categories
    const fetchCategories = async () => {
      try {
        const response = await getAllCategories(); // Fetch the categories
        categories.value = response.map((category: any) => ({
          value: category.id,
          text: category.name,
          subcategories: category.subcategories.map((sub: any) => ({
            value: sub.id,
            text: sub.subcat_name,
          })),
        }));
      } catch (error: any) {
        console.error("Error fetching categories:", error);
      }
    };

    const updateSubcategories = (selectedCategoryId: number) => {
      const selectedCategory = categories.value.find(
        (cat) => cat.value === selectedCategoryId
      );
      subcategories.value = selectedCategory
        ? selectedCategory.subcategories
        : [];
      state.subcategory = null;
    };

    const fetchStatus = async () => {
      try {
        const response = await ticketStatus();
        console.log("Status response:", response.data);

        // Exclude Closed (id = 7) from dropdown options
        status.value = response.data
          .filter((status: any) => status.id !== 7)
          .map((status: any) => ({
            value: status.id,
            text: status.name,
          }));

        // But save all statuses separately (including Closed)
        statuses.value = response.data.map((status: any) => ({
          value: status.id,
          text: status.name,
        }));
      } catch (error: any) {
        console.error("Error fetching statuses:", error);
      }
    };

    const getStatusName = (id: number) => {
      const match = statuses.value.find((s: any) => s.value === id);
      return match ? match.text : "Unknown Status";
    };

    const fetchAssignee = async () => {
      try {
        const response = await users();
        let filteredUsers = response.data.data.map((assignee: any) => ({
          value: assignee.id,
          text: `${assignee.employee_id} - ${assignee.first_name} ${assignee.last_name}`,
          role: assignee.role_id, // Ensure we store role names
        }));

        if (isFromAssignTicket.value) {
          filteredUsers = filteredUsers.filter((user: any) =>
            ["R001", "R002", "R006"].includes(user.role)
          );
        }
        assigned_to.value = filteredUsers;
      } catch (error: any) {
        console.error("Error fetching assignees:", error);
      }
    };
    const fetchPriority = async () => {
      try {
        const response = await apiClient.get("tickets-priority/");

        priority.value = response.data.map((priority: any) => ({
          value: priority.id,
          text: priority.priority_name,
        }));
      } catch (error: any) {
        console.error("Error fetching priority:", error);
      }
    };

    const ticketId = Number((route.params as { ticket_id: string }).ticket_id);

    const ticketDetails = ref<any>({
      ticket_id: 0,
      attachement: [],
      title: "",
      project: "",
      type: "",
      description: "",
      priority_names: "",
      watchers: "",
      created_at: "",
      updated_at: "",
      solved_at: "",
      closed_at: "",
      approvel_status: "",
      approvel_message: "",
      approved_by_name: "",
      cancel_message: "",
      justification: "",
      category: "",
      subcategory: "",
      location_name: "",
      created_by: "",
      created_by_fullname: "",
      assigned_to_fullname: "",
      assigned_to: "",
      status: "",
      approved_by: "",
      due_date: "",
      due_expiry_reason: "",
    });

    const rules = {
      category: {
        required: helpers.withMessage("Category is required", () =>
          String(state.status) === "6" ? helpers.req(state.category) : true
        ),
      },
      subcategory: {
        required: helpers.withMessage("Subcategory is required", () =>
          String(state.status) === "6" ? helpers.req(state.subcategory) : true
        ),
      },
      assigned_to: {
        required: helpers.withMessage("Assignee is required", required),
      },
      priority: {},
      status: {},
      due_date: {},
      due_expiry_reason: {
        required: helpers.withMessage(
          "Due Date Extension reason is required",
          () =>
            showExtensionField.value
              ? helpers.req(state.due_expiry_reason)
              : true
        ),
      },
    };

    const state = reactive<State>({
      category: null,
      subcategory: null,
      assigned_to: null,
      priority: null,
      status: "",
      due_date: null,
      due_expiry_reason: null,
    });

    const initialState = reactive<State>({
      category: null,
      subcategory: null,
      assigned_to: null,
      priority: null,
      status: "",
      due_date: null,
      due_expiry_reason: null,
    });
    const v$ = useVuelidate(rules, state);

    function clear(): void {
      v$.value.$reset();
      Object.assign(state, initialState);
    }

    async function handleSubmit(): Promise<void> {
      if (state.due_date && !(state.status === "7" || state.status === "1")) {
        const selectedDateTime = new Date(state.due_date);
        const now = new Date();
        const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
        if (selectedDateTime.getTime() < oneHourFromNow.getTime()) {
          toast.error("Due date must be at least 1 hour from now.");
          return;
        }
      }

      v$.value.$touch();
      await v$.value.$validate();

      if (v$.value.$error) {
        // Instead of a generic message, show specific field errors
        const errorFields = [];

        // Check each field and collect error messages
        if (v$.value.category.$error) {
          errorFields.push("Category");
        }
        if (v$.value.subcategory.$error) {
          errorFields.push("Subcategory");
        }
        if (v$.value.assigned_to.$error) {
          errorFields.push("Assignee");
        }
        if (v$.value.due_expiry_reason.$error) {
          errorFields.push("Due Date Extension Reason");
        }

        // Display specific error message with missing fields
        if (errorFields.length > 0) {
          const fieldList = errorFields.join(", ");
          toast.error(`Please fill in the required fields: ${fieldList}`);
        } else {
          toast.error("Please correct the form errors before submitting.");
        }

        return;
      }

      try {
        loading.value = true;

        const localDueDate = state.due_date ? new Date(state.due_date) : null;

        const payload = {
          ...state,
          title: ticketDetails.value.title,
          description: ticketDetails.value.description,
          project: ticketDetails.value.project,
          ticket_id: ticketDetails.value.ticket_id,
          due_date: localDueDate
            ? `${localDueDate.getFullYear()}-${String(
                localDueDate.getMonth() + 1
              ).padStart(2, "0")}-${String(localDueDate.getDate()).padStart(
                2,
                "0"
              )}T${String(localDueDate.getHours()).padStart(2, "0")}:${String(
                localDueDate.getMinutes()
              ).padStart(2, "0")}`
            : null,
        };

        const apiUrl = `tickets/${ticketDetails.value.ticket_id}/`;

        await apiClient.put(apiUrl, payload);

        toast.success("Ticket updated successfully!");
        clear();
        await router.push("/all-tickets");
      } catch (error: any) {
        console.error("Error submitting form:", error);

        // Check if the error has a response from the server
        if (
          error.response &&
          error.response.data &&
          error.response.data.message
        ) {
          toast.error(`Error: ${error.response.data.message}`);
        } else {
          toast.error(`Error submitting form: ${error.message}`);
        }
      } finally {
        loading.value = false; // This runs **only after** the API call completes
      }
    }

    // const fetchStatusTracking = async () => {
    //   try {
    //     const statusTracking = await getStatusTracking({ id: ticketId });

    //     // Sort data in descending order by created_at
    //     statusOptions.value = statusTracking.data.sort(
    //       (a: any, b: any) =>
    //         new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    //     );
    //   }catch (error:any) {
    //     console.error("Error fetching ticket data:", error);
    //   }
    // };
    const statusMasterMap = ref<any>({});

    const fetchStatusTracking = async () => {
      try {
        const statusTracking = await getStatusTracking({ id: ticketId });

        // Sort data in descending order by created_at
        statusOptions.value = statusTracking.data.sort(
          (a: any, b: any) =>
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      } catch (error: any) {
        console.error("Error fetching ticket data:", error);
      }
    };

    const fetchStatusMaster = async () => {
      try {
        const response = await getStatus(); // Fetch status master data
        const statusList = response;

        // Convert status master into a dictionary (id -> { statusName, description })
        statusMasterMap.value = statusList.reduce((acc: any, status: any) => {
          acc[status.id] = {
            name: status.name,
            description: status.description,
          };
          return acc;
        }, {});
      } catch (error: any) {
        console.error("Error fetching status master data:", error);
      }
    };

    // Get status text from status master table
    const getStatusText = (statusId: any) => {
      return statusMasterMap.value[statusId]?.name || "Unknown Status";
    };

    // Get status description from status master table
    const getStatusDescription = (statusId: any) => {
      return (
        statusMasterMap.value[statusId]?.description ||
        "No description available"
      );
    };

    // Map status IDs to colors
    const getStatusColor = (statusId: number): string => {
      const colors: Record<number, string> = {
        1: "#1976D2", // Open - Blue
        2: "#FF9800", // In Progress - Orange
        3: "#00BCD4", // New - Cyan
        4: "#9C27B0", // Under Observer - Purple
        5: "#FFC107", // Pending - Yellow
        6: "#4CAF50", // Solved - Green
        7: "#F44336", // Closed - Red
        8: "#E91E63", // Awaiting Approval - Pink
        9: "#009688", // Awaiting User Response - Teal
        10: "#795548", // Cancel - Brown
        11: "#3F51B5", // Awaiting Payment - Indigo
        12: "#FF5722", // Requests For Deployment - Deep Orange
      };

      return colors[statusId] || "#9E9E9E"; // Default Gray if not found
    };

    const formatDate = (dateString: any) => {
      return new Date(dateString).toLocaleString("en-US", {
        month: "short", // Apr
        day: "2-digit", // 29
        year: "numeric", // 2025
        hour: "2-digit", // 06
        minute: "2-digit", // 46
        hour12: true, // PM/AM
      });
    };

    const statusOptions = ref<any>([]);

    onMounted(async () => {
      await fetchCategories();
      await fetchStatus();
      await fetchAssignee();
      await fetchPriority();
      await fetchStatusTracking();
      await fetchFeedbackValues();
      await fetchStatusMaster(); // Fetch status master first
      minDateTime.value = getMinDateTime();
      previousValidDueDate.value = state.due_date;
      currentUser.value = user.id;
      try {
        const getData = await getTicketById({ id: ticketId });
        ticketDetails.value = {
          ...getData,
          justification: getData.justification || "N/A",
          approved_by_name: getData.approved_by_name || "N/A",
          cancel_message: getData.cancel_message || "N/A",
        };
        state.category = getData.category;
        state.subcategory = getData.subcategory;
        state.assigned_to = getData.assigned_to;
        state.priority = getData.priority;
        state.status = getData.status;
        state.due_expiry_reason = getData.due_expiry_reason;
        originalDueDate.value = getData.due_date || null;

        console.log(getData);

        console.log("New:", isReadonly.value);
        console.log("Status:", ticketDetails.value.status);
        console.log("Assigned:", ticketDetails.value.assigned_to);
        console.log("User:", user.id);

        if (
          getData.due_expiry_reason &&
          getData.due_expiry_reason.trim() !== ""
        ) {
          showExtensionField.value = true;
          readonlyDueExtension.value = true;
        } else {
          showExtensionField.value = false;
          readonlyDueExtension.value = false;
        }

        state.due_date = getData.due_date
          ? new Date(getData.due_date).toISOString().slice(0, 16)
          : "";

        Object.assign(initialState, JSON.parse(JSON.stringify(state)));

        const selectedCategory = categories.value.find(
          (cat) => cat.value === getData.category
        );

        if (selectedCategory) {
          subcategories.value = selectedCategory.subcategories;

          const matchedSubcategory = subcategories.value.find(
            (sub) => sub.value === getData.subcategory
          );

          state.subcategory = matchedSubcategory
            ? matchedSubcategory.value
            : null;
        } else {
          state.subcategory = null; // Reset if no matching category is found
        }

        // Convert due_date to datetime-local format
        if (getData.due_date) {
          const dueDate = new Date(getData.due_date);
          state.due_date = dueDate.toISOString().slice(0, 16);
        } else {
          state.due_date = "";
        }
      } catch (error: any) {
        console.error("Error fetching ticket data:", error);
      }

      // try {
      //   const getChat = await getTicketByChat({ ticket_id: ticketId });
      //   comments.value = getChat;
      // }catch (error:any) {
      //   console.error("Error fetching ticket data:", error);
      // }
      //   try {
      //     const statusTracking = await getStatusTracking({ id: ticketId });
      //     statusOptions.value = statusTracking.data;
      //     statusOptions.value = statusTracking.data.sort((a:any, b:any) =>
      //   new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      // );
      //   }catch (error:any) {
      //     console.error("Error fetching ticket data:", error);
      //   }
    });

    // Event handlers for AttachmentDownload component
    const onDownloadStart = (attachment: any) => {
      console.log(
        "Download started for:",
        attachment.display_filename || attachment.original_filename
      );
    };

    const onDownloadComplete = (attachment: any) => {
      console.log(
        "Download completed for:",
        attachment.display_filename || attachment.original_filename
      );
    };

    const onDownloadError = (error: any) => {
      console.error("Download error:", error);
      toast.error(`Failed to download file: ${error.error || "Unknown error"}`);
    };

    return {
      state,
      v$,
      handleSubmit,
      clear,
      ticketDetails,
      categories,
      subcategories,
      status,
      assigned_to,
      assignedToStatusName, // Expose the computed property
      priority,
      loading,
      strings,
      statusOptions,
      showTable,
      showExtensionField,
      currentUser,
      showFeedbackDialog,
      feedbackValues,
      selectedFeedback,
      selectFeedback,
      selectedLabel,
      submitFeedback,
      minDateTime,
      updateSubcategories,
      isEmployee,
      handleClosed,
      handleReopen,
      handleDueDateChange,
      goBack,
      formatDate,
      getStatusText,
      getStatusColor,
      getStatusDescription,
      isFormModified,
      handleCancel,
      // vFeedback$,
      feedbackState,
      feedbackRules,
      feedbackForm,
      feedbackFormIsValid,
      isCreatedByLoggedInUser,
      isReadonly,
      getStatusName,
      isAdmin,
      readonlyDueExtension,
      onDownloadStart,
      onDownloadComplete,
      onDownloadError,
    };
  },
  data: () => ({
    tab: null,
  }),
  methods: {
    // formatDate(dateString: string): string {
    //   return new Date(dateString).toLocaleDateString("en-US", {
    //     year: "numeric",
    //     month: "long",
    //     day: "numeric",
    //   });
    // },
    getFileName(url: string): string {
      return url.split("/").pop() || "";
    },
  },
});
</script>

<style scoped>
.right-col {
  background: #fff;
  border-left: 1px solid #d8d8d8;
  /* height: 100vh; */
}
.right-col span {
  color: #4a4a4a;
  font-weight: 500;
}
.left-col {
  background: #fff;
  border-right: 1px solid #d8d8d8;
  /* height: 100vh; */
}
.select-field {
  cursor: pointer;
}
.card-info {
  background: #026bb1;
  border-radius: 5px;
}
.card-info span {
  color: #7eb9e0;
  font-weight: 500;
  font-size: 14px;
}
.card-info p {
  color: #fff;
  font-weight: 500;
  font-size: 14px;
}
.card-2 {
  border: 1px solid #d8d8d8;
  border-radius: 5px;
}
.small-select {
  max-width: 100%; /* Adjust width as needed */
}
.card-2 span {
  color: #8c8c8c;
  font-size: 14px;
}
.card-2 p {
  color: #2e2e2e;
  font-size: 14px;
  font-weight: 500;
}
.titles {
  color: #2e2e2e;
  font-size: 23px;
  font-weight: 600;
  padding: 10px 0;
}
.submit-btn {
  background-color: #2dcc70;
  color: #fff;
  text-transform: none;
}
.submit-btn:hover {
  background-color: #fff;
  color: #2dcc70;
}
/* timeline-card */
.timeline-card-text {
  height: auto !important;
}

.feedback-card {
  /* padding: 20px; */
  border-radius: 12px;
  background-color: #fff;
}

/* Header */
.feedback-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  font-size: 18px;
}

/* Description */
.feedback-text p {
  font-size: 14px;
  color: #666;
  margin-bottom: 0px;
}

/* Emoji Container */

.emoji-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.emoji-btn {
  background: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: 0.3s;
  font-size: 40px;
  border-radius: 10%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-btn:hover {
  background-color: #f0f0f0;
}

.emoji-btn.selected {
  border: 2px solid #007bff;
}

.emoji-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Submit Button */
.submit-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.submit-button {
  background-color: #007bff;
  color: white;
  text-transform: none;
  font-size: 16px;
  padding: 10px 20px;
  width: 100%;
  border-radius: 8px;
}

.submit-button:hover {
  background-color: #0056b3;
}
.my-textarea .v-messages__message {
  text-align: left !important;
}
.v-messages__message {
  text-align: left !important;
}

.description-text {
  white-space: pre-line;
  word-wrap: break-word;
}

:root .custom-tab-wrapper .tab-content-wrapper .label-value {
  text-transform: none;
}

.required-marker {
  color: #ff5252;
  margin-left: 2px;
}

.required-field .v-field__outline {
  border-color: rgba(0, 0, 0, 0.38);
}

.required-field.v-input--error .v-field__outline {
  border-color: #ff5252 !important;
}

/* Highlight required fields that are empty when form is submitted */
.required-field.v-input--error .v-field__outline {
  border-width: 2px;
}
</style>
