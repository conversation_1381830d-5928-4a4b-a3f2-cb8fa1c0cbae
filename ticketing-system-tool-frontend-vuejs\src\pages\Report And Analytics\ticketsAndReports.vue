<template>
  <div class="page-wrapper">
    <v-row class="add-user-header align-center justify-space-between">
      <v-col cols="12" lg="10" md="10" sm="10">
        <h3 class="page-title">
          {{ strings.ticketsReports.title }}
        </h3>
        <v-breadcrumbs :items="breadcrumbsItems" class="breadcrumbs">
          <template #title="{ item }">
            {{ item.title }}
          </template>
        </v-breadcrumbs>
      </v-col>
    </v-row>

    <!-- Pie Chart Section -->
    <v-row>
      <v-col cols="12" lg="6">
        <v-card>
          <div class="chart-container">
            <div class="chart-header d-flex justify-content-between">
              <p class="chart-title">
                {{ strings.ticketsReports.yearlyReports }}
              </p>
              <select v-model="selectedYearYearly" class="native-dropdown">
                <option v-for="year in years" :key="year" :value="year">
                  {{ year }}
                </option>
              </select>
            </div>
            <div>
              <canvas ref="yearlyChart" />
            </div>
          </div>
        </v-card>
      </v-col>
      <v-col cols="12" lg="6">
        <v-card>
          <div class="chart-container pie-chart-container">
            <p class="chart-title">
              {{ strings.ticketsReports.categoryGraph }}
            </p>
            <canvas id="pie-chart" />
          </div>
        </v-card>
      </v-col>
      <v-col cols="12" lg="6">
        <v-card>
          <div class="chart-container">
            <div class="chart-header d-flex justify-content-between">
              <p class="chart-title">
                {{ strings.ticketsReports.locationGraph }}
              </p>
              <select v-model="selectedYearCategory" class="native-dropdown">
                <option v-for="year in years" :key="year" :value="year">
                  {{ year }}
                </option>
              </select>
            </div>
            <div>
              <canvas ref="categoryChart" />
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, nextTick, watch, defineComponent } from "vue";
import Chart from "chart.js/auto"; // Import Chart.js
import {
  getCategoryReportData,
  getLocationReportData,
  getYearlyReportData,
} from "../../api/apiClient"; // Ensure the API function is correctly imported
import strings from "../../assets/strings.json";
interface YearlyReportItem {
  month: string;
  open: number;
  solved: number;
  closed: number;
}

interface ChartDataItem {
  label: string;
  value: number;
}

export default defineComponent({
  name: "Dashboard",
  setup() {
    const breadcrumbsItems = ref([
      { title: "Home", href: "/" },
      { title: "Report & Analytics", href: "/" },
      { title: "Tickets & Reports", href: "/" },
    ]);
    const ticketCount = ref({
      total_tickets: 0,
      unassigned_tickets: 0,
      pending_tickets: 0,
      solved_tickets: 0,
      closed_tickets: 0,
    });

    const generateYears = () => {
      const currentYear = new Date().getFullYear();
      const startYear = 2016;
      years.value = Array.from(
        { length: currentYear - startYear + 1 },
        (_, i) => startYear + i
      );
    };

    const selectedYearYearly = ref<number>(new Date().getFullYear());
    const selectedYearCategory = ref<number>(new Date().getFullYear());
    const selectedYearEmployee = ref<number>(new Date().getFullYear());
    const employeeReport = ref<any[]>([]);
    const years = ref<number[]>([]);
    const chartData = ref([]);
    const yearlyChart = ref<HTMLCanvasElement | null>(null);
    let chartInstance: Chart | null = null;

    const tableHeaders = ref([
      { title: "Employee ID", key: "employee_id" },
      { title: "Name", key: "name" },
      { title: "Total Tickets", key: "ticket_count" },
    ]);

    // Fetch user details
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    // Fetch and update chart data
    // const fetchYearlyReportData = async () => {
    //   try {
    //     const user = JSON.parse(localStorage.getItem("user") || "{}");
    //     console.log("New", user);
    //     const response = await getYearlyReportData({
    //       year: selectedYearYearly.value,
    //       role: user.role,
    //       user_id: user.id,
    //     });

    //     if (response?.data?.length) {
    //       const reportData = response.data[0]?.data || [];

    //       // Ensure data exists before proceeding
    //       if (!reportData.length) {
    //         console.warn("No yearly report data available.");
    //         return;
    //       }

    //       // Extract data for the chart
    //       const labels = reportData.map((item: any) => item.month);
    //       const openTickets = reportData.map(
    //         (item: any) => item.tickets_count.open || 0
    //       );
    //       const solvedTickets = reportData.map(
    //         (item: any) => item.tickets_count.solved || 0
    //       );
    //       const closedTickets = reportData.map(
    //         (item: any) => item.tickets_count.closed || 0
    //       );

    //       // Ensure nextTick before rendering the chart
    //       await nextTick();
    //       renderChart(labels, openTickets, solvedTickets, closedTickets);
    //     } else {
    //       console.warn("Empty response for yearly report.");
    //     }
    //   }catch (error:any) {
    //     console.error("Failed to fetch yearly report data:", error);
    //   }
    // };

    // Render Chart.js Mixed Bar/Line Chart
    const renderChart = (
      labels: string[],
      open: number[],
      solved: number[],
      closed: number[]
    ) => {
      if (!yearlyChart.value) {
        console.error("Chart canvas element is missing.");
        return;
      }

      if (chartInstance) {
        chartInstance.destroy();
      }

      chartInstance = new Chart(yearlyChart.value, {
        type: "bar",
        data: {
          labels,
          datasets: [
            {
              label: "Open Tickets",
              type: "bar",
              data: open,
              backgroundColor: "rgba(255, 99, 132, 0.6)",
              borderWidth: 1,
            },
            {
              label: "Solved Tickets",
              type: "line",
              data: solved,
              borderColor: "rgba(54, 162, 235, 1)",
              borderWidth: 2,
              fill: false,
            },
            {
              label: "Closed Tickets",
              type: "bar",
              data: closed,
              backgroundColor: "rgba(75, 192, 192, 0.6)",
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              title: {
                display: true,
                text: "Months",
              },
            },
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: "Number of Tickets",
              },
            },
          },
        },
      });
    };

    const categoryChart = ref<HTMLCanvasElement | null>(null);
    let categoryChartInstance: Chart | null = null;

    // const fetchCategoryReportData = async () => {
    //   try {
    //     const user = JSON.parse(localStorage.getItem("user") || "{}");

    //     console.log("Fetching category report data for:", user);

    //     if (!user.role || !user.id) {
    //       console.error("User role or ID is missing");
    //       return;
    //     }

    //     const response = await getCategoryReportData({
    //       year: selectedYearCategory.value,
    //       role: user.role,
    //       user_id: user.id,
    //     });

    //     if (!response?.data?.length) {
    //       console.warn("No category report data available.");
    //       return;
    //     }

    //     // Extract data for the stacked bar chart
    //     const categories = response.data.map((item: any) => item.category_name);
    //     const subcategories = [
    //       ...new Set(
    //         response.data.flatMap((item: any) =>
    //           item.subcategory_data.map((sub: any) => sub.subcategory_name)
    //         )
    //       ),
    //     ];

    //     const dataset = subcategories.map((subcategory) => {
    //       return {
    //         label: subcategory,
    //         data: categories.map((category: any) => {
    //           const categoryItem = response.data.find(
    //             (item: any) => item.category_name === category
    //           );
    //           const subcategoryItem = categoryItem?.subcategory_data.find(
    //             (sub: any) => sub.subcategory_name === subcategory
    //           );
    //           return subcategoryItem ? subcategoryItem.total_tickets : 0;
    //         }),
    //         backgroundColor: `rgba(${Math.floor(
    //           Math.random() * 255
    //         )}, ${Math.floor(Math.random() * 255)}, ${Math.floor(
    //           Math.random() * 255
    //         )}, 0.6)`,
    //         borderWidth: 1,
    //       };
    //     });

    //     // Render Chart
    //     await nextTick();
    //     renderCategoryChart(categories, dataset);
    //   }catch (error:any) {
    //     console.error("Failed to fetch category report data:", error);
    //   }
    // };

    const renderCategoryChart = (labels: string[], dataset: any[]) => {
      if (!categoryChart.value) {
        console.error("Category chart canvas element is missing.");
        return;
      }

      if (categoryChartInstance) {
        categoryChartInstance.destroy();
      }

      categoryChartInstance = new Chart(categoryChart.value, {
        type: "bar",
        data: {
          labels,
          datasets: dataset,
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              stacked: true,
              title: {
                display: true,
                text: "Categories",
              },
            },
            y: {
              stacked: true,
              title: {
                display: true,
                text: "Number of Tickets",
              },
            },
          },
          plugins: {
            legend: {
              position: "top",
            },
          },
        },
      });
    };

    const fetchReportData = async () => {
      try {
        const response = await getLocationReportData();
        chartData.value = response.data.map((item: any) => ({
          label: item.location_name,
          value: item.total_tickets,
        }));
        createPieChart(); // Call the chart creation method after fetching data
      } catch (error: any) {
        console.error("Failed to fetch report data", error);
      }
    };

    const createPieChart = () => {
      // Get the canvas element and cast it to HTMLCanvasElement
      const ctx = document.getElementById("pie-chart") as HTMLCanvasElement;
      if (!ctx) return; // Ensure the element exists

      const labels = chartData.value.map((item: any) => item.label);
      const data = chartData.value.map((item: any) => item.value);
      const colors = chartData.value.map(
        () =>
          `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(
            Math.random() * 255
          )}, ${Math.floor(Math.random() * 255)}, 0.6)`
      );

      // Create the pie chart
      new Chart(ctx, {
        type: "pie",
        data: {
          labels: labels,
          datasets: [
            {
              label: "Tickets by Location",
              data: data,
              backgroundColor: colors,
              borderColor: colors.map((color) => color.replace("0.6", "1")),
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: "bottom",
            },
          },
        },
      });
    };

    onMounted(async () => {
      try {
        generateYears();
        // await fetchCategoryReportData();
        // await fetchYearlyReportData();
        fetchReportData();
      } catch (error: any) {
        console.error(error);
      }
    });

    watch(selectedYearYearly, async () => {
      // await fetchYearlyReportData();
    });

    watch(selectedYearCategory, async () => {
      // await fetchCategoryReportData();
    });

    return {
      user,
      breadcrumbsItems,
      ticketCount,
      years,
      selectedYearEmployee,
      selectedYearYearly,
      selectedYearCategory,
      tableHeaders,
      categoryChart,
      yearlyChart,
      strings,
    };
  },
});
</script>

<style scoped>
.add-user-header {
  margin-bottom: 20px;
}

.breadcrumbs {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: grey;
}
.native-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  display: flex;
  justify-content: space-between;
  padding: 5px 18px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
}
.chart-container {
  border-radius: 5px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
canvas {
  flex-grow: 1;
  width: 100%;
  height: auto;
}

.pie-chart-container {
  height: 300px; /* Adjust the height as needed */
  width: 300px; /* Adjust the width as needed */
  margin: 0 auto; /* Center align */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

#pie-chart {
  width: 100%;
  height: 100%;
}
</style>
