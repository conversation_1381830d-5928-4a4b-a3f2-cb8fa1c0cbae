:root {
  .app-sidebar {
    margin: 0;
    top: 10px !important;
    left: 10px !important;
    height: calc(100% - 25px) !important;
    border: 0;
    display: none;
    .v-navigation-drawer__content {
      h1 {
        display: block;
        margin: 0;
        text-align: center;
        padding: 10px;
        a {
          display: block;
        }
      }
      /* Single Root Menu */
      & > .v-list {
        & > .v-list-item {
          padding-top: 0;
          padding-bottom: 0;
          gap: 0;
          & > .v-list-item__overlay {
          }
          & > .v-list-item__underlay {
          }
          & > .v-list-item__prepend {
            img {
            }
          }
          & > .v-list-item__content {
            .v-list-item-title {
              padding: 8px;
              color: #4e4e4e;
            }
          }
          &.active-item {
            background-color: #2dcc70;
            & > .v-list-item__content {
              .v-list-item-title {
                font-weight: 600;
              }
            }
          }
        }
        & > div[class*="v-list"] {
          margin-top: 10px;
        }
      }
      /* Multi Menu */
      .v-list-group {
        & > .v-list-item {
          padding-top: 0;
          padding-bottom: 0;
          gap: 0;
          & > .v-list-item__overlay {
          }
          & > .v-list-item__underlay {
          }
          & > .v-list-item__prepend {
            img {
            }
          }
          & > .v-list-item__content {
            .v-list-item-title {
              padding: 8px;
            }
          }
          &.active-item {
            background-color: #2dcc70;
          }
        }
        &.v-list-group--open {
          background-color: #ebfff3;
          & > .v-list-item {
            background-color: #2dcc70;
            gap: 0;
            & > .v-list-item__overlay {
            }
            & > .v-list-item__underlay {
            }
            & > .v-list-item__prepend {
              img {
                filter: brightness(0) invert(1);
              }
            }
            & > .v-list-item__append {
              i {
                color: #fff;
                opacity: 0.5;
              }
            }
            & > .v-list-item__content {
              .v-list-item-title {
                color: #fff;
                font-weight: 600;
              }
            }
          }
        }
        & > .v-list-group__items {
          & > .v-list {
            padding: 0;
            & > .v-list-item {
              padding-inline-start: 30px !important;
              padding-top: 9px;
              padding-bottom: 9px;
              & > .v-list-item__overlay {
              }
              & > .v-list-item__underlay {
              }
              & > .v-list-item__content {
                .submodule-container {
                  text-align: left;
                  font-size: 13px;
                  padding: 0;
                  color: #2aa05c !important;
                }
              }
            }
            & + .v-list {
              margin-top: 0px;
            }
            &.active-item {
              & > .v-list-item {
                & > .v-list-item__content {
                  .submodule-container {
                    font-weight: 600;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.text-size {
  font-size: 14px !important;
}
.v-list-item-title {
  font-size: 14px;
  width: 100%;
  align-content: center;
  color: #a9a9a9;
  align-items: center;
  text-align: left;
  padding: 10px;
  cursor: pointer;
}
.submodule-container {
  text-align: center;
  padding: 0;
}
.v-list-group {
  margin: 0;
  padding: 0;
}
.custom-icon {
  width: 18px;
  height: 18px;
}
.active-icon {
  filter: brightness(0) invert(1);
}
.active-item .v-list-item-title {
  color: white !important;
}
.active-item .custom-icon {
  filter: brightness(0) invert(1);
}
.v-list-item--density-default.v-list-item--one-line {
  min-height: fit-content;
  gap: 12px;
}
.menu-icon {
  padding: 12px;
}
.v-menu__content {
  right: 0 !important;
  left: auto;
}
.app-header-wrapper {
  display: flex;
  justify-content: space-between;
  margin: 10px 35px 0 25px;
  border-radius: 5px;
  position: fixed !important;
  top: 0%;
  right: 0 !important;
  &::before, &::after{
    content: ('');
    height: 10px;
    background: #f0f0f0;
    width: 100%;
    position: absolute;
    top: -10px;
    left: 0;
  }
  &::after{
    top: auto;
    bottom: -10px;
  }
}
.nav-bar {
  margin: 10px 10px 0 10px;
  border-radius: 5px;
  height: 100vh;
  position: fixed !important;
}
.header-content {
  display: flex;
  justify-content: space-between;
}
.active-group {
  background-color: rgba(45, 204, 112, 0.1);
}
.user-initials {
  font-size: 16px;
  font-weight: bold;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.user-name {
  font-size: 14px;
  padding-left: 10px;
}
