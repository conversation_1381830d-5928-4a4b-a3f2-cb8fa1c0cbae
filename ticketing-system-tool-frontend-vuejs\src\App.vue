<script lang="ts" setup>
import { useRoute } from "vue-router";
import { computed } from "vue";
import Default from "../src/layouts/default.vue";

const route = useRoute();
const requiresAuth = computed(() => route?.meta?.requiresAuth ?? false);
import { onMounted ,onUnmounted } from "vue";

import { connectLogoutSocket, disconnectLogoutSocket } from "./utils/socket";

onMounted(() => {
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  if (user?.id) {
    console.log("Connecting logout socket for user:", user.id);
    connectLogoutSocket(user.id);
  }
});

// Disconnect socket when component unmounts
onUnmounted(() => {
  console.log("Disconnecting logout socket");
  disconnectLogoutSocket();
});

// Watch for user changes in localStorage
window.addEventListener('storage', (event) => {
  if (event.key === 'user') {
    const user = JSON.parse(event.newValue || "{}");
    if (user?.id) {
      console.log("User changed, reconnecting logout socket");
      connectLogoutSocket(user.id);
    } else {
      disconnectLogoutSocket();
    }
  }
});

</script>

<template>
  <div class="app-main">
    <!-- Render Default layout for authenticated routes -->
    <div v-if="requiresAuth">
      <Default>
        <router-view />
      </Default>
    </div>
    <!-- Directly render public routes -->
    <router-view v-else />
  </div>
</template>

<style>
@import "./assets/css/font.css";
@import "./styles/style.scss";
.app-page-layout {
  position: absolute;
  margin: 0 10px;
  width: 100%;
  border-radius: 5px;
}
</style>
