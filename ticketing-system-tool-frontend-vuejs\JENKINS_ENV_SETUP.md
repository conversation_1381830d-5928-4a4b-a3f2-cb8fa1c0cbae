# Jenkins Environment Setup Guide

This guide explains how to set up and use environment-specific configurations with <PERSON> for the Nex-ticketing-v1 project.

## Overview

The project is configured to use different environment configurations based on the branch:

- **develop branch**: Uses staging configuration
  - API URL: `http://internal-project.nexware-global.com:9019/api/`
  - WebSocket URL: `ws://localhost:8000/ws/ticket/`

- **main branch**: Uses production configuration
  - API URL: `https://ticket.nexware-global.com:9049/api/`
  - WebSocket URL: `wss://internal-project.nexware-global.com:8001/ws/ticket/`

## Files and Scripts

The following files are used to manage environment configurations:

1. **Jenkinsfile**: Defines the Jenkins pipeline with branch-specific build configurations
2. **generate-env.sh**: Script to generate environment files for different environments
3. **Package.json scripts**: Contains npm scripts to generate environment files and build for different environments

## How It Works

When code is pushed to a branch:

1. <PERSON> detects the push via a webhook
2. <PERSON> checks out the code
3. Based on the branch (develop or main), <PERSON>:
   - Generates the appropriate environment file (.env.staging or .env.production)
   - Builds the application with the correct configuration
   - Deploys the build to the appropriate server

## Setting Up Jenkins

1. Create a new <PERSON> pipeline job
2. Configure it to use the <PERSON>file from the repository
3. Set up branch-specific build triggers:
   - develop branch: Builds with staging configuration
   - main branch: Builds with production configuration

## Manual Environment File Generation

You can manually generate environment files using the provided npm scripts:

```bash
# For development
npm run generate-env:dev

# For staging
npm run generate-env:staging

# For production
npm run generate-env:prod
```

## Building for Different Environments

After generating the environment files, you can build the application:

```bash
# For staging
npm run build:staging

# For production
npm run build:prod
```

## Modifying Environment Variables

If you need to change the environment variables:

1. Edit the `generate-env.sh` script
2. Update the appropriate section for the environment you want to modify
3. Commit and push the changes

## Troubleshooting

If you encounter issues with the environment configuration:

1. Check that the environment files are being generated correctly
2. Verify that the Jenkinsfile is using the correct scripts
3. Ensure that the Jenkins job is configured to use the Jenkinsfile
4. Check the Jenkins build logs for any errors

## Additional Resources

- See `jenkins-webhook-config.md` for information on setting up webhooks
- See `ENV_SETUP.md` for general information about environment configuration
