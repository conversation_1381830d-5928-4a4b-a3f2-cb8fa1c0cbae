importScripts("https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.6.1/firebase-messaging-compat.js");

const firebaseConfig = {
  apiKey: "AIzaSyADgEFyILgrB37ileMyGZZqP3czdwCpn8g",
  authDomain: "nex-ticket-edf83.firebaseapp.com",
  projectId: "nex-ticket-edf83",
  storageBucket: "nex-ticket-edf83.firebasestorage.app",
  messagingSenderId: "642495260773",
  appId: "1:642495260773:web:a18296d81ef7e1b23bfe8c",
  measurementId: "G-SQCFF7PFP5"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
const messaging = firebase.messaging();

// Handle background notifications
messaging.onBackgroundMessage((payload) => {
  console.log("Received background message: ", payload);

  self.registration.showNotification(payload.notification.title, {
    body: payload.notification.body,
    icon: "/firebase-logo.png"
  });
});
