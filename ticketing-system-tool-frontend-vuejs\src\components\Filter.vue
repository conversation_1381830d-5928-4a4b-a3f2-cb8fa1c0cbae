<!-- FilterSection.vue -->
<template>
  <v-row
    align="center"
    justify="space-between"
  >
    <v-col
      cols="12"
      sm="12"
      md="9"
      lg="10"
    >
      <v-row align="center">
        <v-col
          cols="12"
          lg="4"
          md="6"
          sm="12"
        >
          <div class="form-group d-block">
            <input
              id="searchValue"
              v-model="localSearchValue"
              type="text"
              class="form-input"
              :placeholder="searchPlaceholder"
              @input="updateSearchValue"
            >
          </div>
        </v-col>
        <v-col
          v-for="(filter, index) in filters"
          :key="index"
          cols="12"
          lg="2"
          md="6"
          sm="3"
        >
          <div class="form-group d-block">
            <select
              :id="filter.id"
              v-model="localFilterValues[filter.id]"
              :class="localFilterValues[filter.id] === '' ? 'placeholder' : ''"
              class="form-input"
              @change="updateFilterValue(filter.id)"
            >
              <option :value="''">
                {{ filter.placeholder }}
              </option>
              <option
                v-for="option in filter.options"
                :key="option.value"
                :value="option.value"
              >
                {{ option.text }}
              </option>
            </select>

            <!-- <select
              :id="filter.id"
              v-model="localFilterValues[filter.id]"
              :class="localFilterValues[filter.id] === '' ? 'placeholder' : ''"
              class="form-input"
              @change="updateFilterValue(filter.id)"
            >
              <option :value="''">{{ filter.placeholder }}</option>
              <option
                v-for="option in filter.options"
                :key="option.value"
                :value="option.text"
              >
                {{ option.text }}
              </option>
            </select> -->
          </div>
        </v-col>
      </v-row>
    </v-col>
    <v-col cols="auto">
      <div class="btn-toolbar">
        <v-btn
          icon="mdi-magnify"
          variant="tonal"
          @click="fetchFilteredUsers"
        />
        <v-btn
          icon="mdi-cached"
          variant="text"
          @click="resetFilter"
        />
      </div>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import { defineComponent, ref, watch, type PropType } from "vue";

export default defineComponent({
  name: "FilterSection",
  props: {
    searchValue: String,
    searchPlaceholder: {
      type: String,
      default: "Search...",
    },
    filters: {
      type: Array as PropType<
        Array<{
          id: string;
          placeholder: string;
          options: Array<{ value: string; text: string }>;
        }>
      >,
      default: () => [],
    },
  },
  emits: ["update:searchValue", "update:filterValues"],
  setup(props, { emit }) {
    const localSearchValue = ref<any>(props.searchValue || "");
    const localFilterValues = ref<{ [key: string]: string }>({});

    const updateSearchValue = () => {
      emit("update:searchValue", localSearchValue.value);
    };

    const updateFilterValue = (filterId: string) => {
      emit("update:filterValues", { ...localFilterValues.value });
    };

    watch(
      () => props.searchValue,
      (newValue) => {
        localSearchValue.value = newValue;
      }
    );

    return {
      localSearchValue,
      localFilterValues,
      updateSearchValue,
      updateFilterValue,
      fetchFilteredUsers() {
        console.log(
          "Fetch filtered users with",
          localSearchValue.value,
          localFilterValues.value
        );
      },
      resetFilter() {
        localSearchValue.value = "";
        localFilterValues.value = {};
        emit("update:searchValue", "");
        emit("update:filterValues", {});
      },
    };
  },
});
</script>

<style scoped>
.text-blue {
  color: #1e88e5;
}
.text-decoration-none {
  text-decoration: none;
}
</style>
<style scoped>
.manage-user-container {
  margin: 5px;
  padding: 16px;
  height: 100vh;
  font-family: "Roboto", sans-serif;
  overflow-y: auto;
}
/* Page Header */
.page-header {
  display: flex;
  align-items: center;
  gap: 10px;
}
.page-header h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
}
.data-count {
  background-color: #026bb1;
  border-radius: 16px;
  color: #fff;
  padding: 4px 12px;
  font-size: 12px;
}
.breadcrumbs {
  padding: 0;
  margin: 0;
  font-size: 14px;
  margin-bottom: 1rem;
  color: #757575;
}
/* search & filter */
.search-filter-section {
  background-color: #f9f9f9;
  border-radius: 5px;
  margin: 5px;
  padding: 10px 5px;
}
.dropdown-default-value {
  color: #b5b5b5;
}
.form-section-container {
  padding: 12px;
}
.form-input {
  width: 100%;
  font-size: 14px;
  border: 1px solid #ccc;
  padding: 15px;
  border-radius: 4px;
  box-sizing: border-box;
  background-color: #fff;
}
.form-input:focus {
  border-color: #000;
}
.placeholder {
  color: gray;
}

.search-btn {
  background-color: #2dcc70;
  color: #fff;
  border: none;
}
.refresh-btn {
  background-color: #e6e6e6;
  color: #b5b5b5;
  border: none;
}
.filtered-btn {
  display: flex;
  justify-content: space-evenly;
}

/* Dialog Model */

/* For Mobile Devices*/
@media (max-width: 600px) {
  .action-button {
    display: flex;
    justify-content: space-evenly;
  }
}

/* For Tablets */
@media (min-width: 601px) and (max-width: 960px) {
}

/* For Laptops and Desktops */
@media (min-width: 961px) {
}
</style>
