:root {
  /* Base Styles */
  .app-layout {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-image: url("../assets/images/login-bg.png");
    background-size: cover;
    background-position: center;
    padding: 30px;
  }

  .app-login-wrapper {
    display: flex;
    background-color: #fff;
    border-radius: 10px;
    max-width: 900px;
    width: 100%;
    &.secondary-login-wrapper{
      max-width: 100%;
      padding: 60px;
      .left{
        width: 100%;
        img{
          width: 65%;
        }
      }
      .right{
        padding: 0;
        max-width: none;
        min-width: 420px;
        figure{
          display: block;
          text-align: center;
          img{
            height: 220px;
            margin: 0 auto;
          }
        }
      }
    }
    .v-input{
      & > .v-input__control{
        .v-field{
          .v-field__prepend-inner{
            i{

            }
          }
          .v-field__field{
            .v-label{

            }
          }
          .v-field__input{
            min-height: 52px;
          }
        }
      }
      .v-input__details{
        padding-inline: 0;
      }
    }
    .btn-toolbar{
      .v-btn{
        padding: 12px;
        --v-btn-height: 42px;
        --v-btn-size: 1rem;
      }
    }
    a{
      color: #026BB1;
      text-decoration: none !important;
      &:hover{
        text-decoration: none !important;
      }
    }
  }

  .left {
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px 0;
  }

  .left img {
    width: 80%;
  }

  .right {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 40px;
    background-color: #fff;
  }

  .logo {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
  }

  .logo img {
    max-width: 200px;
    height: auto;
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0;
    text-align: center;
  }

  p {
    color: #555;
    margin-bottom: 20px;
    text-align: center;
  }
  .login-link{
    font-size: 14px;
    text-align: right;
  }

  /* Responsive Design */
  @media (max-width: 959px) {
    .app-login-wrapper{
      &.secondary-login-wrapper{
        padding: 0;
        .right{
          display: block;
        }
      }
    }
  }

  @media (max-width: 780px) {
    /* Tablets */
    .app-login-wrapper {
      flex-direction: column;
      text-align: center;
      padding: 60px 60px 0;
    }

    .left {
      width: 100%;
      margin-bottom: 20px;
    }

    .right {
      width: 100%;
    }
  }

  @media (max-width: 768px) {
    /* Small screens */
    .app-login-wrapper {
      padding: 30px;
    }

    .right {
      padding: 20px;
    }

    h2 {
      font-size: 1.2rem;
    }

    .left img {
      display: none;
    }
  }

  @media (max-width: 480px) {
    /* Mobile screens */
    .app-layout {
      padding: 10px;
    }

    .app-login-wrapper {
      padding: 20px;
    }

    .left img {
      display: none;
    }

    .right {
      padding: 15px;
    }

    h2 {
      font-size: 1rem;
    }
  }
}
