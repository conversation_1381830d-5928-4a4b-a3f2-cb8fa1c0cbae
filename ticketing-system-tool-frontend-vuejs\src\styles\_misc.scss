:root {
  /* Misc */
  .min-h-100 {
    min-height: 100%;
  }
  .p-50{
    padding: 50px !important;
  }

  .v-chip.data-count {
    background-color: #026bb1;
    border-radius: 16px;
    color: #fff;
    padding: 4px 12px;
    font-size: 12px;
    height: auto;
  }

  /* Dropdown Menu */
  .v-menu {
    & > .v-overlay__content {
      & > .v-list {
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
        padding: 15px;
        border: 1px solid #e4e4e4;

        & > .v-list-item {
          padding-inline: 0;
          gap: 0;
          padding: 0;
          margin-top: 12px;
          transition: none;

          .v-list-item__overlay {
            background: none;
          }

          .v-list-item__content {
            .v-list-item-title {
              padding: 0;
              color: #8c8b90;
              line-height: 1.25rem;
              font-weight: 500;
              display: flex;
              align-items: center;
            }

            i {
              font-size: 16px;
              opacity: 0.5;
            }
          }

          &:first-child {
            margin-top: 0;
          }

          &:hover {
            .v-list-item__content {
              .v-list-item-title {
                color: #026bb1;
              }

              i {
                opacity: 1;
              }
            }
          }
        }
        &.notification-list {
          width: 400px;
          background: #fff;
          border-radius: 8px;
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
          padding: 46px 25px 25px !important;
          .notification-title {
            font-size: 18px;
            font-weight: 700;
            padding: 0;
          }
          .notify-panel-head {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: fixed;
            width: calc(100% - 60px);
            background: #ffffff;
            top: 1px;
            padding: 10px 0;
            z-index: 1;
            .v-btn {
              box-shadow: none;
              height: auto;
              width: auto;
              padding: 6px 0;
              font-size: 12px;
              color: #ef4d56 !important;
              line-height: normal;
              background: #ffeced !important;
            }
          }
          .v-list-item {
            .v-list-item__content {
              width: 100%;
              .v-list-item-title {
                &.no-notifications {
                  display: block;
                  text-align: center;
                  padding: 40px 0;
                  font-size: 20px;
                  font-weight: 300;
                  i {
                    font-size: 30px;
                  }
                  span {
                    display: block;
                  }
                }
              }
            }
          }

          .unread {
            font-weight: bold;
            background-color: #f5f5f5;
          }
          .notification-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            border: 0;
            line-height: normal;
            white-space: pre-wrap;
            .v-list-item__content {
              .v-list-item-title {
                white-space: pre-wrap;
              }
            }
          }
          .mark-read-btn {
            font-size: 12px;
            text-transform: none;
          }
        }
      }
    }
  }

  /* Hightlight Card */
  .card-hightlight-default {
    box-shadow: none;
    height: calc(100vh - 220px);
    background: #e9f6ff;
    color: #5f7c90;
    border-radius: 5px;
    padding: 20px;
    text-align: left;
    a{
      color: #5f7c90;
      &:hover{
        color: #4c687c;
      }
    }

    .v-expansion-panels{
      .v-expansion-panel{
        background-color: transparent;
        border: 0;
        .v-expansion-panel__shadow{
          box-shadow: none;
        }
        & > button, & > .v-expansion-panel-title{
          background-color: #eef8ff;
          border: 1px solid #a9c4d5;
          padding: 10px 15px;
          border-radius: 0;
          line-height: normal;
          color: #5f7c90;
          font-weight: 500;
          &.v-expansion-panel-title--active{
            background: #5f7c90;
            color: #fff;
            border-color: #5f7c90;
            font-weight: 600;
            &:hover{
              background: #5f7c90;
              color: #fff;
              border-color: #5f7c90;
              font-weight: 600;
            }
          }
          .v-expansion-panel-title__overlay{
            display: none;
          }
          &:hover{
            background-color: #fff;
            color: #5f7c90;
          }
        }
        .v-expansion-panel-text{
          border: 1px solid #5f7c90;
          border-top: 0;
          .v-expansion-panel-text__wrapper{
            padding: 12px;
            background-color: #fff;
          }
        }
        &::after{
          display: none;
        }
        &+.v-expansion-panel{
          margin-top: 15px;
        }
      }
    }

    .v-card-title {
      padding: 0 0 5px 0;
    }

    .v-card-text {
      padding: 0;
      overflow: auto;
      height: 95%;
    }

    h5 {
      color: #026bb1;
      margin: 0;
      font-weight: 600;
    }

    p {
      text-align: left;
    }

    ul {
      margin: 0 0 0 20px;
      padding: 0;
      list-style: none;

      &.fa-ul {
        & > li {
          .fa-li {
            top: 3px;
            font-size: 75%;
          }

          & + li {
            margin-top: 6px;
          }
        }
      }
    }
  }

  /* Card */
  .card {
    border-color: #f0f0f0;

    .card-title {
      font-family: "SF Pro Display";
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
    }
  }

  /* Data Table */
  .v-table {
    & > .v-table__wrapper {
      & > table {
        & > thead {
          tr {
            th {
              background-color: #2e2e2e;
              color: #fff;
              height: auto;
              padding: 8px 12px;
              font-size: 16px;
              font-weight: 600;

              .v-data-table-header__content {
                i {
                  font-size: 14px;
                  padding: 3px 0 0 5px;
                }
              }

              &.v-data-table__th--sortable,
              &:hover {
                color: #fff;
              }
            }
          }
        }

        & > tbody {
          tr {
            td {
              height: auto;
              padding: 8px 12px;
              font-size: 15px;
            }
          }
        }
      }
    }
  }

  /* Upload */
  .v-file-upload {
    display: block;
    transition: 0.5s;
    text-align: center;
    color: #b0b0b0;

    .v-file-upload-icon {
      font-size: 50px;
    }

    .v-file-upload-title {
      font-weight: 400;
      letter-spacing: 0;
    }

    &:hover {
      color: #2e2e2e;
    }
  }

  .upload-help-block {
    text-align: left;

    & > label {
      display: block;
      opacity: 0.35;
      letter-spacing: 0;
    }

    .v-btn {
      font-size: 12px;
      padding: 4px 8px;
      font-weight: 500;
      height: auto;
    }
  }

  .custom-upload-icon {
    .v-input__prepend {
      background: #2dcc70;
      color: #fff;
      border-radius: 5px;
      padding: 10px;

      &:hover {
        background: #1aad57;
      }
    }
  }

  /* Upload Document Files */
  .batch-document-wrapper {
    margin-left: -10px;
    & > .batch-documents {
      margin-left: 10px;
      margin-top: 10px;
      a {
        color: #026bb1;
      }
    }
  }

  /* Column Style */
  .ticket-status-wrapper {
    width: 250px;
    margin-right: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    .scroll-content {
      padding: 15px;
      overflow-y: auto;
    }
    .card-container {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin: 0;
      cursor: move;
      .single-ticket-card {
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.06);
        border: 1px solid #ececec;
        .ticket-title {
          white-space: wrap;
          letter-spacing: 0;
          padding: 0.75rem 0.75rem 0;
          h5 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
            opacity: 0.8;
          }
        }
        .v-card-text {
          padding: 0.25rem 0.75rem 0;
          p {
            text-align: left;
            margin-bottom: 10px;
            line-height: normal;
            opacity: 0.8;
          }
        }
        .ticket-date-badge {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: 10px;
          padding: 3px 10px;
          font-size: 12px;
          line-height: normal;
          gap: 3px;
          color: #9d9d9d;
          border: 1px solid #cacaca;
          i {
            font-size: 100% !important;
          }
        }
        .ticket-expired {
          color: #8a8a8a;
          background: #eaeaea;
          border: 1px solid transparent;
        }

        .single-ticket-card-footer {
          display: flex;
          justify-content: space-between;
          padding: 5px 0;
          font-size: 11px;
          & > small {
            font-size: 100%;
            font-weight: 500;
            opacity: 0.7;
          }
        }
        &:hover {
          background: #fff;
        }
        .v-card__overlay,
        .v-card__underlay {
          display: none;
        }
      }
    }
    &.ticket-status-inprogress {
      // background: #fff4e1;
      .ticket-status-title {
        //color: #FCB230;
      }
    }
    &.ticket-status-open {
      // background: #f4e4f6;
      .ticket-status-title {
        //color: #A131B2;
      }
    }
    &.ticket-status-assigned {
      // background: #ecf4ff;
      .ticket-status-title {
        //color: #397AE0;
      }
    }
    &.ticket-status-new {
      // background: #e0fdf9;
      .ticket-status-title {
        //color: #47D0BE;
      }
    }
    &.ticket-status-underobserver {
      // background: #e8e8ff;
      .ticket-status-title {
        //color: #6969fb;
      }
    }
    &.ticket-status-pending {
      .ticket-status-title {
        //color: #FF5625;
      }
    }
  }
  .ticket-status-title {
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    padding: 11px 15px;
    gap: 6px;
    margin: 0;
    background: #f8f8f8;
    border-bottom: 1px solid #d8d8d8;
    i {
      font-size: 65%;
    }
  }

  /* Tab Syles */
  .custom-tab-wrapper {
    box-shadow: none;

    .v-tabs {
      .v-slide-group__container {
        .v-slide-group__content {
          gap: 10px;

          .v-btn {
            background: #f4f4f4;
            border-radius: 5px 5px 0 0;

            .v-btn__overlay {
            }

            .v-btn__underlay {
            }

            &.v-tab--selected {
              background: #2e2e2e;

              .v-btn__content {
                color: #fff;
                font-weight: 600;
              }
            }

            .v-btn__content {
              color: #7b7b7b;
              font-size: 0.975rem;
              font-weight: 500;

              .v-tab__slider {
                background: #2e2e2e;
              }
            }
          }
        }
      }
    }

    .v-card-text {
      border: 1px solid #d8d8d8;
      border-radius: 0 5px 5px 5px;
      min-height: 320px;
      padding: 25px;
    }

    .tab-content-wrapper {
      font-size: 15px;

      .label-name {
        color: #8c8c8c;
      }

      .label-value {
        font-weight: 500;
        text-transform: capitalize;
      }

      .tab-section-title {
        font-size: 19px;
        font-weight: 600;
        .comment-count-badge {
          background-color: #f3fff8;
          border: 1px solid #bfe9d1;
          color: #2dcc70;
          font-size: 12px;
          font-weight: normal;
          line-height: normal;
          display: inline-flex;
          padding: 4px 10px;
          border-radius: 30px;
          margin-left: 3px;
        }
      }
    }
  }

  /* Accordian */
  .v-expansion-panels {
    .v-expansion-panel {
      .v-expansion-panel__shadow {
        box-shadow: none;
      }

      .v-expansion-panel-title {
        background: #ffffff;
        color: #2e2e2e;
        min-height: inherit;
        padding: 15px;
        border: 1px solid #c2c2c2;

        h3 {
          margin: 0;
          font-size: 17px;
          font-weight: 500;
        }

        .v-expansion-panel-title__icon {
        }

        &.v-expansion-panel-title--active {
          background: #2e2e2e;
          border-color: #2e2e2e;
          color: #fff;
        }
      }

      .v-expansion-panel-text {
        border: 1px solid #2e2e2e;

        .v-expansion-panel-text__wrapper {
          padding: 0;
        }
      }
    }
  }

  /* Inset Round Badge */
  .ticket-switch-btn{
    position: absolute;
    right: 0;
    top: 100px;
    z-index: 1111;
    & > .v-input{
      &.custom-switch{
        .v-input__control{
          .v-selection-control{
            border: 1px solid #016bb1;
            padding: 0 15px;
            border-radius: 30px;
            background: #f6fbff;
            .v-selection-control__wrapper{
              .v-switch__track{
                background: #016bb1;
                opacity: 1;
              }
              .v-selection-control__input{
                &::before{

                }
                input{

                }
                .v-switch__thumb{

                }
              }
            }
            &.v-selection-control--dirty{
              border: 1px solid #2DCC70;
              background: #f0fff6;
              margin-top: 14px;
              .v-selection-control__wrapper{
                color: #2DCC70 !important;
                .v-switch__track{
                  background: #2DCC70 !important;
                  opacity: 1;
                  height: 30px;
                }
                .v-selection-control__input{
                  &::before{
  
                  }
                  input{
  
                  }
                  .v-switch__thumb{
                    width: 18px;
                    height: 18px;
                  }
                }
                .v-label{
  
                }
              }
              .v-label{
                color: #2DCC70;
              }
            }
            .v-label{
              font-size: 1.15rem;
              font-weight: 600;
              color: #016bb1;
              letter-spacing: 0;
            }
          }
        }
      }
    }
  }

  /* Misc Pages */
  .page-misc{
    padding: 40px;
    background: #ffffff;
    background: radial-gradient(circle, rgba(255, 255, 255, 1) 50%, rgba(230, 230, 230, 1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: absolute;
    width: 100%;
    height: 100%;
    text-align: center;
    img{
      height: 280px;
      width: auto;
    }
    .page-misc-content{
      padding: 30px 0 0 0;
      h1{
        display: block;
        font-size: 42px !important;
        color: #2e2e2e;
      }
      p{
        padding: 0 25%;
        font-size: 20px;
        line-height: 30px;
        margin: 0;
        opacity: 0.8;
      }
      .btn-toolbar{
        justify-content: center;
        margin: 1rem 0 0 0;
        .v-btn{
          font-size: 16px;
          padding: 10px 20px;
          height: auto;
        }
      }
    }
  }
  .box-select-action{
    .v-card{
      box-shadow: none !important;
      border: 1px solid #D8D8D8;
      padding: 15px;
      text-align: center;
      &.selected-export{
        border: 1px solid #53d282;
        label{
          font-weight: 600;
          opacity: 1;
        }
      }
      &:hover{
        border: 1px dashed #53d282;
        label{
          opacity: 0.75;
        }
        .v-card__overlay{
          background: none
        }
      }
      label{
        display: block;
        font-weight: 500;
        opacity: 0.5;
        letter-spacing: 0;
        cursor: pointer;
      }
    }
  }

  /* File Preview */
 .file-preview-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 18px;
    .file-preview {
      position: relative;
      text-align: center;
      width: 90px;
    }
    .preview-image, .file-icon {
      width: 80px;
      height: 80px;
      object-fit: cover;
      border-radius: 8px;
      border: 1px dashed #c8c8c8;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: inset 0 0 10px 0 rgba(0, 0, 0, 0.05);
      position: relative;
      i{
        &.mdi-file-pdf-box, &.mdi-file-pdf{
          color: #b30c00;
        }
        &.mdi-file-word{
          color: #1872D9;
        }
        &.mdi-file-excel{
          color: #3CA241;
        }
        &.mdi-file-document{
          color: #2e2e2e;
        }
      }
    }
    .file-name {
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: calc(100% - 10px);
      margin-top: 3px;
      opacity: 0.65;
    }
    .remove-btn {
      position: absolute;
      top: -5px;
      right: 0px;
      background: #ffe2e4;
      width: 26px;
      color: #EF4D56;
      height: 26px;
      font-weight: 800;
      border: 2px solid #ffffff;
      box-shadow: none;
      &:hover{
        background: #EF4D56;
        color: #fff;
      }
    }
   }

  .helper-text{
    display: block;
    font-size: 13px;
    opacity: 0.4;
    padding-top: 5px;
  }

  /* No Data Available */
  .no-data-available-wrapper{
    display: flex;
    flex-direction: column;
    color: #7b7b7b;
    margin: 50px 0 0 0;
    figure{
      margin: 0;
      img{
        height: 70px;
      }
    }
    label{
      display: block;
      font-weight: 700;
      margin-top: 6px;
    }
  }

  /* Status Background */
  .bg-success {
    background-color: #7dd420 !important;
  }

  .bg-info {
    background-color: #5900d9 !important;
  }

  .bg-warning {
    background-color: #ffab00 !important;
  }

  .bg-danger {
    background-color: #f75d81 !important;
  }

  /* Table */
  .table {
    color: #2e2e2e;

    & > thead {
      & > tr {
        & > th {
          font-family: "SF Pro Display";
          font-size: 0.938rem;
          font-weight: 600;
          padding: 0.65rem;
          vertical-align: middle;
        }
      }

      &.thead-dark {
        & > tr {
          & > th {
            background-color: #2e2e2e;
            border-color: #2e2e2e;
            border-bottom: 2px solid #2e2e2e;
          }
        }
      }
    }

    tbody {
      & > tr {
        & > td {
          font-size: 0.875rem;
          padding: 0.65rem;
          vertical-align: middle;
        }

        &:nth-child(even) {
          background-color: #fbfafa;
        }
      }
    }
  }

  /* Popover */
  .popover-dark {
    .popover {
      font-family: "Archivo";
      background-color: #2e2e2e;
      color: #fff;
      border: 0;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);

      .popover-body {
        color: #fff;
        padding: 0.85rem 1.25rem 1rem;

        & > ul {
          display: block;
          margin: 0;
          padding: 0;
          list-style: none;

          & > li {
            & + li {
              margin-top: 0.15rem;
            }
          }
        }
      }
    }

    .bs-popover-top > .arrow::before,
    .bs-popover-auto[x-placement^="top"] > .arrow::before {
      border-top-color: #2e2e2e;
    }

    .bs-popover-top > .arrow::after,
    .bs-popover-auto[x-placement^="top"] > .arrow::after {
      border-top-color: #2e2e2e;
    }

    .bs-popover-left > .arrow::before,
    .bs-popover-auto[x-placement^="left"] > .arrow::before {
      border-left-color: #2e2e2e;
    }

    .bs-popover-left > .arrow::after,
    .bs-popover-auto[x-placement^="left"] > .arrow::after {
      border-left-color: #2e2e2e;
    }

    .bs-popover-right > .arrow::before,
    .bs-popover-auto[x-placement^="right"] > .arrow::before {
      border-right-color: #2e2e2e;
    }

    .bs-popover-right > .arrow::after,
    .bs-popover-auto[x-placement^="right"] > .arrow::after {
      border-right-color: #2e2e2e;
    }

    .bs-popover-bottom > .arrow::before,
    .bs-popover-auto[x-placement^="bottom"] > .arrow::before {
      border-right-color: #2e2e2e;
    }

    .bs-popover-bottom > .arrow::after,
    .bs-popover-auto[x-placement^="bottom"] > .arrow::after {
      border-right-color: #2e2e2e;
    }
  }

  /* Status Badge */
  .status-badge {
    background-color: #2e2e2e;
    color: #fff;
    border-radius: 10rem;
    font-size: 0.813rem;
    line-height: normal;
    padding: 0.6rem 1rem;
    font-weight: 500;

    &.status-valid {
      background-color: #39cc09;
    }

    &.status-extend {
      background-color: #5900d9;
    }

    &.status-warning {
      background-color: #ffab00;
    }

    &.status-invalid {
      background-color: #f75d81;
    }
  }
}

/* Responsive */
@media screen and (max-width: 767px) {
  :root{
    .page-misc{
      padding: 40px;
      img{
        width: 80%;
        height: auto;
      }
      .page-misc-content{
        h1{
          display: block;
          font-size: 28px !important;
          color: #2e2e2e;
        }
        p{
          padding: 0;
          font-size: 16px;
          line-height: 26px;
        }
        .btn-toolbar{
          .v-btn{
            font-size: 16px;
            padding: 10px 20px;
          }
        }
      }
    }
  }
}
