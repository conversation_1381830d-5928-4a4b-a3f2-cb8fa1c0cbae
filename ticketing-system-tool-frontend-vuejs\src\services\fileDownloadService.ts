import axios from 'axios';
import { useToast } from 'vue-toastification';

interface Attachment {
  id: number;
  attachement: string;
  original_filename: string;
  display_filename: string;
  file_url?: string;
}

export class FileDownloadService {
  private toast = useToast();

  /**
   * Download a file using the attachment object
   * @param attachment - The attachment object from API response
   */
  async downloadFile(attachment: Attachment): Promise<{ success: boolean; error?: string }> {
    try {
      const token = localStorage.getItem('jwt_token') || localStorage.getItem('token');
      
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Use file_url if available, otherwise construct the download URL
      const downloadUrl = attachment.file_url || `/api/attachments/download/${attachment.id}/`;
      
      const response = await axios({
        method: 'GET',
        url: downloadUrl,
        headers: {
          'Authorization': `Bearer ${token}`
        },
        responseType: 'blob' // Important for file downloads
      });

      // Create blob URL and trigger download
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      
      // Use display_filename or original_filename for download
      link.download = attachment.display_filename || attachment.original_filename || 'download';
      
      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the blob URL
      window.URL.revokeObjectURL(url);

      this.toast.success(`File "${link.download}" downloaded successfully!`);
      return { success: true };

    } catch (error: any) {
      console.error('Download failed:', error);
      
      let errorMessage = 'Failed to download file';
      
      if (error.response) {
        switch (error.response.status) {
          case 401:
            errorMessage = 'Authentication failed. Please login again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to download this file.';
            break;
          case 404:
            errorMessage = 'File not found.';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later.';
            break;
          default:
            errorMessage = `Download failed: ${error.response.statusText}`;
        }
      } else if (error.request) {
        errorMessage = 'Network error. Please check your connection.';
      } else {
        errorMessage = error.message || 'Unknown error occurred';
      }

      this.toast.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Download multiple files
   * @param attachments - Array of attachment objects
   */
  async downloadMultipleFiles(attachments: Attachment[]): Promise<void> {
    for (const attachment of attachments) {
      await this.downloadFile(attachment);
      // Add a small delay between downloads to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  /**
   * Get file extension from filename
   * @param filename - The filename
   */
  getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * Get file icon based on file extension
   * @param filename - The filename
   */
  getFileIcon(filename: string): string {
    const extension = this.getFileExtension(filename);
    
    const iconMap: { [key: string]: string } = {
      pdf: 'mdi-file-pdf',
      doc: 'mdi-file-word',
      docx: 'mdi-file-word',
      xls: 'mdi-file-excel',
      xlsx: 'mdi-file-excel',
      ppt: 'mdi-file-powerpoint',
      pptx: 'mdi-file-powerpoint',
      txt: 'mdi-file-document',
      jpg: 'mdi-file-image',
      jpeg: 'mdi-file-image',
      png: 'mdi-file-image',
      gif: 'mdi-file-image',
      zip: 'mdi-folder-zip',
      rar: 'mdi-folder-zip',
      '7z': 'mdi-folder-zip',
    };

    return iconMap[extension] || 'mdi-file-document';
  }
}

// Create a singleton instance
export const fileDownloadService = new FileDownloadService();
