<template>
  <div class="page-wrapper page-dashboard">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <h2 class="page-title">
            {{ strings.dashboard.title }}
          </h2>
          <v-breadcrumbs :items="breadcrumbsItems" class="breadcrumbs">
            <template #item="{ item }">
              <span>{{ item.title }}</span>
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col cols="auto"></v-col>
      </v-row>
    </div>

    <div class="page-content-wrapper">
      <!-- Dashboard Card Component -->
      <DashboardCard
        :ticket-count="ticketCount"
        :user="user"
        class="mt-2"
        @card-clicked="handleCardClick"
      />

      <!-- Graph/Chart Section -->
      <section class="widget-charts-wrapper mt-3">
        <v-row>
          <v-col cols="12" lg="6">
            <div class="chart-container">
              <div class="chart-header">
                <h3 class="chart-title">
                  {{ strings.dashboard.reports.yearlyReport }}
                </h3>
                <Datepicker
                  v-model="yearlyDateRange"
                  :enable-time-picker="false"
                  range
                  format="yyyy-MM-dd"
                  placeholder=datepickerPlaceholder
                  class="daterange"
                  :disabled-dates="disableFutureDates"
                  :clearable="
                    !yearlyDateRange || yearlyDateRange.some((date) => !date)
                  "
                />
              </div>
              <div class="chart-canvas-wrapper">
                <canvas ref="yearlyChart" />
              </div>
            </div>
          </v-col>
          <v-col cols="12" lg="6">
            <div class="chart-container">
              <div class="chart-header">
                <h3 class="chart-title">
                  {{ strings.dashboard.reports.categoryReport }}
                </h3>
                <Datepicker
                  v-model="categoryDateRange"
                  :enable-time-picker="false"
                  range
                  format="yyyy-MM-dd"
                  placeholder=datepickerPlaceholder
                  class="daterange"
                  :disabled-dates="disableFutureDates"
                  :clearable="
                    !categoryDateRange ||
                    categoryDateRange.some((date) => !date)
                  "
                />
              </div>
              <div class="chart-canvas-wrapper">
                <canvas ref="categoryChart" />
              </div>
            </div>
          </v-col>
        </v-row>
      </section>

      <!-- Employee Report Section -->
      <section v-if="isSuperAdmin" class="widget-section-wrapper mt-4 pb-4">
        <div class="widget-card">
          <div class="widget-header">
            <h3 class="widget-title">
              {{ strings.dashboard.reports.employeeReport }}
            </h3>
            <div class="widget-actions">
              <Datepicker
                v-model="dateRange"
                :enable-time-picker="false"
                range
                format="yyyy-MM-dd"
                placeholder=datepickerPlaceholder
                class="daterange"
                :disabled-dates="disableFutureDates"
                :clearable="!dateRange || dateRange.some((date) => !date)"
                :default-view="dateRange[0]"
              />
              <div class="dropdown-wrapper">
                <select
                  id="dropdown"
                  v-model="selectedValue"
                  class="dropdown"
                  @focus="isOpen = true"
                  @blur="handleBlur"
                >
                  <option
                    v-for="option in options"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </option>
                </select>
                <span
                  :class="['dropdown-icon', { open: isOpen }]"
                  aria-hidden="true"
                >
                  <v-icon size="18">mdi-chevron-down</v-icon>
                </span>
              </div>
              <v-btn
                variant="tonal"
                class="ml-2"
                @click="showExportDialog = true"
              >
                {{ strings.dashboard.exportButton }}
              </v-btn>
            </div>
          </div>
          <div class="widget-container mt-3">
            <v-data-table
              :headers="tableHeaders"
              :items="employeeReport"
              item-value="id"
              hide-default-footer
            >
              <template #[`item.ticket_count`]="{ item }">
                <span>{{ item.ticket_count }}</span>
              </template>
            </v-data-table>
          </div>
        </div>
      </section>
      <v-dialog v-model="showExportDialog" max-width="500">
        <v-card>
          <v-card-title class="headline">
            <h5>{{strings.dashboard.exportDialogTitle}}</h5>
            <v-btn
              icon="mdi-close"
              variant="text"
              class="dialog-close-btn"
              @click="closeExportDialog"
            />
          </v-card-title>
          <v-card-text class="p-50">
            <v-row justify="space-around" class="box-select-action">
              <v-col>
                <v-card
                  outlined
                  :elevation="selectedExportType === 'excel' ? 4 : 1"
                  :class="{ 'selected-export': selectedExportType === 'excel' }"
                  style="cursor: pointer"
                  @click="selectedExportType = 'excel'"
                >
                  <v-icon size="50" color="green">mdi-microsoft-excel</v-icon>
                  <label>{{strings.dashboard.exportOptions.excel}}</label>
                </v-card>
              </v-col>
              <v-col>
                <v-card
                  outlined
                  :elevation="selectedExportType === 'pdf' ? 4 : 1"
                  :class="{ 'selected-export': selectedExportType === 'pdf' }"
                  style="cursor: pointer"
                  @click="selectedExportType = 'pdf'"
                >
                  <v-icon size="50" color="red">mdi-file-pdf-box</v-icon>
                  <label>{{strings.dashboard.exportOptions.pdf}}</label>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-btn @click="closeExportDialog"> {{strings.dashboard.buttons.close}} </v-btn>
            <v-btn
              variant="tonal"
              :disabled="!selectedExportType"
              @click="submitExport"
            >
              {{strings.dashboard.buttons.submit}}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
  </div>
</template>

<script lang="ts">
import {
  exportEmployeeReportData,
  getCategoryReportData,
  getEmployeeReportData,
  getTicketCount,
  getYearlyReportData,
} from "../api/apiClient";
import {
  computed,
  defineComponent,
  nextTick,
  onMounted,
  ref,
  watch,
} from "vue";
import Chart from "chart.js/auto";
// import * as XLSX from "xlsx";
import strings from "../../src/assets/strings.json";
import DashboardCard from "../../src/components/DashboardCard.vue";
import Datepicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import * as XLSX from "xlsx-js-style";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { useRouter } from "vue-router";

type YearlyChartData = {
  month: string;
  open: number;
  solved: number;
  closed: number;
};

interface Option {
  value: string;
  label: string;
}

export default defineComponent({
  name: "Dashboard",
  components: {
    DashboardCard,
    Datepicker,
  },
  setup() {
    const breadcrumbsItems = ref([
      { title: "Home", href: "/" },
      { title: "Dashboard" },
    ]);
    const options = ref<Option[]>([
      { value: "1", label: "All" },
      { value: "2", label: "Active" },
      { value: "3", label: "Inactive" },
      { value: "4", label: "Resigned" },
    ]);
    const ticketCount = ref({
      total_tickets: 0,
      unassigned_tickets: 0,
      pending_tickets: 0,
      solved_tickets: 0,
      closed_tickets: 0,
    });
    const selectedValue = ref<string>("1");
    const isOpen = ref(false);

    const generateYears = () => {
      const currentYear = new Date().getFullYear();
      const startYear = 2016;
      years.value = Array.from(
        { length: currentYear - startYear + 1 },
        (_, i) => startYear + i
      );
    };

    const selectedYearYearly = ref<number>(new Date().getFullYear());
    const selectedYearCategory = ref<number>(new Date().getFullYear());
    const selectedYearEmployee = ref<number>(new Date().getFullYear());
    const selectedDateRange = ref<string[]>([]); // Holds the selected date range
    const employeeReport = ref<any[]>([]);
    const years = ref<number[]>([]);
    const chartData = ref([]);
    const yearlyChart = ref<HTMLCanvasElement | null>(null);
    let chartInstance: Chart | null = null;
    const tableHeaders = ref([
      { title: "Employee ID", key: "employee_id" },
      { title: "Name", key: "name" },
      { title: "Total Tickets", key: "ticket_count" },
    ]);
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    const isSuperAdmin = computed(() => user.role === "R001");
    const firstFiveEmployees = computed(() => employeeReport.value.slice(0, 5));
    const showExportDialog = ref(false);
    const selectedExportType = ref<"excel" | "pdf" | null>(null);
    const router = useRouter();

    // In your setup() function, replace the current default dateRange with the following:
    const today = new Date();
    const disableFutureDates = (date: Date) => {
      return date > today;
    };
    const startDate = new Date();
    startDate.setDate(today.getDate() - 29); // 30 days including today

    const dateRange = ref<[Date, Date]>([startDate, today]);

    const yearlyDateRange = ref<[Date, Date]>([startDate, today]);

    const categoryDateRange = ref<[Date, Date]>([startDate, today]);

    const formatDate = (date: Date): string => {
      return `${date.getFullYear()}-${(date.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;
    };

    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    const selectedLabel = computed(() => {
      const selectedOption = options.value.find(
        (opt) => opt.value === selectedValue.value
      );
      return selectedOption ? selectedOption.label : "None";
    });

    const handleBlur = () => {
      setTimeout(() => {
        isOpen.value = false;
      }, 150);
    };

    const handleCardClick = (card: any) => {
      const label = card.label;

      if (label === "Unassigned") {
        if (user.role === "R001" || user.role === "R002") {
          router.push("/assign-ticket");
        }
      } else if (
        label === "Pending Approval" ||
        label === "Awaiting Approval"
      ) {
        router.push("/pending-approval");
      } else {
        router.push({
          path: "/all-tickets",
          query: {
            view: "list",
            status: card.status,
          },
        });
      }
    };

    const fetchEmployeeReport = async () => {
      try {
        if (!dateRange.value || dateRange.value.length !== 2) {
          console.error("Please select a valid date range.");
          return;
        }

        // Convert Date objects to "yyyy-MM-dd" format
        const formatDate = (date: Date) =>
          `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}/${date.getFullYear()}`;

        const start_date = formatDate(dateRange.value[0]);
        const end_date = formatDate(dateRange.value[1]);
        const selectedOption = selectedValue.value;

        const response = await getEmployeeReportData(
          start_date,
          end_date,
          selectedOption
        );

        if (response?.data) {
          employeeReport.value = [...response.data];
        } else {
          employeeReport.value = [];
        }
      } catch (error: any) {
        console.error("Failed to fetch employee report data:", error);
      }
    };

    const submitExport = () => {
      if (selectedExportType.value === "excel") {
        exportToCSV();
      } else if (selectedExportType.value === "pdf") {
        exportToPDF();
      }
      showExportDialog.value = false;
      selectedExportType.value = null;
    };

    const closeExportDialog = () => {
      showExportDialog.value = false;
      selectedExportType.value = null;
    };

    const setDefaultDateRange = () => {
      const today = new Date();
      const startDate = new Date();
      startDate.setDate(today.getDate() - 29); // Last 30 days (including today)
      dateRange.value = [startDate, today];
    };

    const exportToPDF = async () => {
      try {
        if (!dateRange.value || dateRange.value.length !== 2) {
          console.error("Please select a valid date range.");
          return;
        }

        const formatDateForAPI = (date: Date) =>
          `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}/${date.getFullYear()}`;

        const formatDateForPDF = (date: Date) =>
          `${date.getFullYear()}-${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;

        const start_date = formatDateForAPI(dateRange.value[0]);
        const end_date = formatDateForAPI(dateRange.value[1]);
        const reportPeriod = `${formatDateForPDF(
          dateRange.value[0]
        )} to ${formatDateForPDF(dateRange.value[1])}`;
        const selectedOption = selectedValue.value;

        const response = await exportEmployeeReportData(
          start_date,
          end_date,
          selectedOption
        );

        if (!response || !response.data) {
          console.error("No data available for export.");
          return;
        }

        const employeeReport = response.data;

        const doc = new jsPDF();

        // Add header
        doc.setFontSize(16);
        doc.text("Employees Ticket Report", 14, 15);

        // Add general info
        const info = [
          ["Report Period", reportPeriod],
          ["Employee Count", employeeReport.length.toString()],
          [
            "Total Tickets",
            employeeReport
              .reduce((sum: any, emp: any) => sum + emp.ticket_count, 0)
              .toString(),
          ],
          ["Created Date", new Date().toISOString().split("T")[0]],
        ];

        info.forEach((line, index) => {
          doc.setFontSize(10);
          doc.text(`${line[0]}: ${line[1]}`, 14, 25 + index * 6);
        });

        // Add table
        const tableBody =
          employeeReport.length > 0
            ? employeeReport.map((emp: any) => [
                emp.employee_id,
                emp.name,
                emp.ticket_count.toString(),
                emp.projects && emp.projects.length > 0
                  ? emp.projects.every(
                      (p: any) =>
                        p.project_name === "-" || p.total_tickets === 0
                    )
                    ? "-"
                    : emp.projects
                        .filter(
                          (p: any) =>
                            p.project_name !== "-" && p.total_tickets > 0
                        )
                        .map(
                          (p: any) => `${p.project_name} (${p.total_tickets})`
                        )
                        .join(", ")
                  : "-",
              ])
            : [["-", "No Data", "0", "-"]];

        autoTable(doc, {
          startY: 55,
          head: [
            ["Employee ID", "Employee Name", "Total Tickets", "Project Names"],
          ],
          body: tableBody,
          styles: { fontSize: 9 },
          headStyles: { fillColor: [25, 118, 210] },
        });

        doc.save(`Employee_Report_${reportPeriod.replace(/ /g, "_")}.pdf`);
      } catch (error: any) {
        console.error("Error exporting PDF:", error);
      }
    };

    const exportToCSV = async () => {
      try {
        if (!dateRange.value || dateRange.value.length !== 2) {
          console.error("Please select a valid date range.");
          return;
        }

        //Convert date format to "dd/MM/yyyy" for API request
        const formatDateForAPI = (date: Date) =>
          `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}/${date.getFullYear()}`;

        //Convert date format to "YYYY-MM-DD" for Excel report filename
        const formatDateForExcel = (date: Date) =>
          `${date.getFullYear()}-${(date.getMonth() + 1)
            .toString()
            .padStart(2, "0")}-${date.getDate().toString().padStart(2, "0")}`;

        const start_date = formatDateForAPI(dateRange.value[0]); // API format
        const end_date = formatDateForAPI(dateRange.value[1]); // API format
        const reportPeriod = `${formatDateForExcel(
          dateRange.value[0]
        )} to ${formatDateForExcel(dateRange.value[1])}`;
        const selectedOption = selectedValue.value; // Active, Inactive, Resigned

        //Fetch Data from API (Fix ReferenceError)
        const response = await exportEmployeeReportData(
          start_date,
          end_date,
          selectedOption
        );

        if (!response || !response.data) {
          console.error("No data available for export.");
          return;
        }

        const employeeReport = response.data; // Store fetched data

        if (employeeReport.length === 0) {
          console.warn(
            "Employee report is empty. Still exporting an empty file."
          );
        }

        //Define `generalInfo` AFTER `reportPeriod` is initialized
        const generalInfo = [
          ["Report Period:", reportPeriod],
          ["Employee Count:", employeeReport.length],
          [
            "Ticket Count:",
            employeeReport.reduce(
              (sum: any, emp: any) => sum + emp.ticket_count,
              0
            ),
          ],
          ["Created Date:", new Date().toISOString().split("T")[0]],
        ];

        // Define headers
        const headers = [
          ["Employee ID", "Employee Name", "Total Tickets", "Project Names"],
        ];

        //Map employee data including project names
        const mappedData =
          employeeReport.length > 0
            ? employeeReport.map((row: any) => [
                row.employee_id,
                row.name,
                row.ticket_count,
                row.projects && row.projects.length > 0
                  ? row.projects.every(
                      (p: any) =>
                        p.project_name === "-" || p.total_tickets === 0
                    )
                    ? "-"
                    : row.projects
                        .filter(
                          (p: any) =>
                            p.project_name !== "-" && p.total_tickets > 0
                        )
                        .map(
                          (p: any) => `${p.project_name} (${p.total_tickets})`
                        )
                        .join(", ")
                  : "-",
              ])
            : [["-", "No Data", "0", "-"]];

        // Create a new workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet([
          ...generalInfo,
          [""],
          ...headers,
          ...mappedData,
        ]);

        //  Style headers
        headers[0].forEach((_, colIndex) => {
          const cellRef = XLSX.utils.encode_cell({
            r: generalInfo.length + 1,
            c: colIndex,
          });
          if (ws[cellRef]) {
            ws[cellRef].s = {
              font: { bold: true },
              fill: { fgColor: { rgb: "D9D9D9" } },
            };
          }
        });

        //  Set column widths
        ws["!cols"] = [{ wch: 15 }, { wch: 20 }, { wch: 15 }, { wch: 30 }];

        //  Append worksheet to workbook and save as file
        XLSX.utils.book_append_sheet(wb, ws, "Employee Report");
        XLSX.writeFile(
          wb,
          `Employee_Report_${reportPeriod.replace(/ /g, "_")}.xlsx`
        );
      } catch (error: any) {
        console.error("Error exporting data:", error);
      }
    };

    // Fetch and update chart data
    const fetchYearlyReportData = async () => {
      try {
        if (!yearlyDateRange.value || yearlyDateRange.value.length !== 2) {
          console.error(
            "Please select a valid date range for the yearly report."
          );
          return;
        }

        const startDate = formatDate(yearlyDateRange.value[0]);
        const endDate = formatDate(yearlyDateRange.value[1]);

        const response = await getYearlyReportData({
          start_date: startDate,
          end_date: endDate,
          role: user.role,
          user_id: user.id,
        });

        // Make sure response?.data is in the format you expect
        if (response?.data?.data?.length) {
          const reportData = response.data.data;
          // Example: "period" is your x-axis label from the backend
          const labels = reportData.map((item: any) => item.period);
          const openTickets = reportData.map(
            (item: any) => item.tickets_count.open
          );
          const solvedTickets = reportData.map(
            (item: any) => item.tickets_count.solved
          );
          const closedTickets = reportData.map(
            (item: any) => item.tickets_count.closed
          );

          await nextTick();
          renderChart(labels, openTickets, solvedTickets, closedTickets);
        } else {
          console.warn("Empty or invalid response for yearly report.");
        }
      } catch (error: any) {
        console.error("Failed to fetch yearly report data:", error);
      }
    };

    const renderChart = (
      labels: string[],
      open: number[],
      solved: number[],
      closed: number[]
    ) => {
      if (!yearlyChart.value) {
        console.error("Chart canvas element is missing.");
        return;
      }

      if (chartInstance) {
        chartInstance.destroy();
      }

      chartInstance = new Chart(yearlyChart.value, {
        type: "bar",
        data: {
          labels, // These labels come directly from the backend "period" field.
          datasets: [
            {
              label: "Open Tickets",
              type: "bar",
              data: open,
              backgroundColor: "rgba(255, 99, 132, 0.6)",
              borderWidth: 1,
            },
            {
              label: "Solved Tickets",
              type: "line",
              data: solved,
              borderColor: "rgba(54, 162, 235, 1)",
              borderWidth: 2,
              fill: false,
            },
            {
              label: "Closed Tickets",
              type: "line",
              data: closed,
              backgroundColor: "rgba(255, 0, 0, 1)",
              borderWidth: 2,
              fill: false,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              title: {
                display: true,
                text: strings.dashboard.yearlyReportLabel.lableXAxis,
              },
            },
            y: {
              beginAtZero: true,
              suggestedMax: Math.max(...open, ...solved, ...closed) + 5,
              title: {
                display: true,
                text: strings.dashboard.yearlyReportLabel.lableYAxis,
              },
              ticks: {
                stepSize: 5,
                maxTicksLimit: 10,
              },
            },
          },
        },
      });
    };

    const categoryChart = ref<HTMLCanvasElement | null>(null);
    let categoryChartInstance: Chart | null = null;

    const fetchCategoryReportData = async () => {
      try {
        if (!user.role || !user.id) {
          console.error("User role or ID is missing");
          return;
        }

        // Check that a valid date range is selected.
        if (!categoryDateRange.value || categoryDateRange.value.length !== 2) {
          console.error(
            "Please select a valid date range for the category report."
          );
          return;
        }

        // Format the selected dates to "yyyy-MM-dd"
        const startDate = formatDate(categoryDateRange.value[0]);
        const endDate = formatDate(categoryDateRange.value[1]);

        const response = await getCategoryReportData({
          start_date: startDate,
          end_date: endDate,
          role: user.role,
          user_id: user.id,
        });

        if (!response?.data?.length) {
          console.warn("No category report data available.");
          const emptyLabels: string[] = ["No Data"];
          const emptyDataset = [
            {
              label: "No Data",
              data: [0],
              backgroundColor: "rgba(200,200,200,0.4)",
              borderWidth: 1,
            },
          ];
          await nextTick();
          renderCategoryChart(emptyLabels, emptyDataset);
          return;
        }

        const sortedData = [...response.data]
          .sort((a, b) => {
            const totalA = a.subcategory_data.reduce(
              (sum: number, s: any) => sum + s.total_tickets,
              0
            );
            const totalB = b.subcategory_data.reduce(
              (sum: number, s: any) => sum + s.total_tickets,
              0
            );
            return totalB - totalA;
          })
          .slice(0, 5);

        const categories = sortedData.map((item: any) => item.category_name);
        const subcategories = [
          ...new Set(
            response.data.flatMap((item: any) =>
              item.subcategory_data.map((sub: any) => sub.subcategory_name)
            )
          ),
        ];

        const dataset = subcategories.map((subcategory) => {
          return {
            label: subcategory,
            data: categories.map((category: any) => {
              const categoryItem = response.data.find(
                (item: any) => item.category_name === category
              );
              const subcategoryItem = categoryItem?.subcategory_data.find(
                (sub: any) => sub.subcategory_name === subcategory
              );
              return subcategoryItem ? subcategoryItem.total_tickets : 0;
            }),
            backgroundColor: `rgba(${Math.floor(
              Math.random() * 255
            )}, ${Math.floor(Math.random() * 255)}, ${Math.floor(
              Math.random() * 255
            )}, 0.6)`,
            borderWidth: 1,
          };
        });

        // Render the Category Report chart with the new data
        await nextTick();
        renderCategoryChart(categories, dataset);
      } catch (error: any) {
        console.error("Failed to fetch category report data:", error);
      }
    };

    const renderCategoryChart = (labels: string[], dataset: any[]) => {
      if (!categoryChart.value) {
        console.error("Category chart canvas element is missing.");
        return;
      }

      if (categoryChartInstance) {
        categoryChartInstance.destroy();
      }

      categoryChartInstance = new Chart(categoryChart.value, {
        type: "bar",
        data: {
          labels,
          datasets: dataset,
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              stacked: true,
              title: {
                display: true,
                text: "Categories",
              },
            },
            y: {
              stacked: true,
              title: {
                display: true,
                text: "Number of Tickets",
              },
              ticks: {
                stepSize: 2,
                maxTicksLimit: 2,
              },
            },
          },
          plugins: {
            legend: {
              position: "top",
              display: false,
            },
          },
        },
      });
    };

    onMounted(async () => {
      try {
        if (!user.role || !user.id) {
          console.error("User role or ID is missing");
          return;
        }
        const body = {
          role: user.role,
          user_id: user.id,
        };
        const response = await getTicketCount(body);
        ticketCount.value = response.data;
        generateYears();
        setDefaultDateRange();
        await nextTick();
        selectedValue.value = "1";
        await fetchEmployeeReport();
        await fetchCategoryReportData();
        await fetchYearlyReportData();
      } catch (error: any) {
        console.error(error);
      }
    });

    watch(selectedValue, async () => {
      if (dateRange.value && dateRange.value.length === 2) {
        await fetchEmployeeReport();
      }
    });

    watch(
      dateRange,
      async (newRange) => {
        if (newRange && newRange.length === 2) {
          await fetchEmployeeReport();
        }
      },
      { deep: true }
    );

    watch(selectedYearEmployee, async () => {
      try {
        if (!user.role || !user.id) {
          console.error("User role or ID is missing");
          return;
        }

        const body = {
          role: user.role,
          user_id: user.id,
        };

        // Fetch updated ticket count and employee report
        const response = await getTicketCount(body);
        ticketCount.value = response.data;
      } catch (error: any) {
        console.error("Failed to update data on year change:", error);
      }
    });

    watch(
      yearlyDateRange,
      async (newRange) => {
        console.log("yearlyDateRange changed:", newRange);
        if (newRange && newRange.length === 2) {
          await fetchYearlyReportData();
        }
      },
      { deep: true, immediate: true }
    );

    watch(
      categoryDateRange,
      async (newRange) => {
        console.log("categoryDateRange changed:", newRange);
        if (newRange && newRange.length === 2) {
          await fetchCategoryReportData();
        }
      },
      { deep: true, immediate: true }
    );

    return {
      user,
      isOpen,
      isSuperAdmin,
      breadcrumbsItems,
      ticketCount,
      years,
      selectedYearEmployee,
      selectedYearYearly,
      selectedYearCategory,
      tableHeaders,
      firstFiveEmployees,
      employeeReport,
      categoryChart,
      yearlyChart,
      strings,
      selectedDateRange,
      dateRange,
      selectedLabel,
      selectedValue,
      options,
      handleBlur,
      exportToCSV,
      handleCardClick,
      showExportDialog,
      submitExport,
      selectedExportType,
      exportToPDF,
      yearlyDateRange,
      categoryDateRange,
      closeExportDialog,
      disableFutureDates,
    };
  },
});
</script>

<style scoped>
/* Header Styles */
.add-user-header {
  margin-bottom: 20px;
}

/* Breadcrumbs */
.breadcrumbs {
  margin: 0;
  padding: 0;
  font-size: 14px;
  color: #757575;
  margin-bottom: 1rem;
}

/* Table Styles */
.daterange {
  width: 250px;
  display: block;
  background: white;
}
.enhanced-table {
  border-radius: 10px;
  border: 1px solid #ddd;
  margin-top: 10px;
  background-color: #fff;
}
.employee-toolbar {
  margin-bottom: 10px;
}
.employee-table .v-data-table__th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: bold;
  text-transform: uppercase;
}
.employee-table .v-data-table__tr:hover {
  background-color: #f1f5ff;
  cursor: pointer;
}

/* Dropdown Styling */
.year-dropdown {
  border: 1px solid #ccc;
  text-align: center;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  min-width: 70px;
  background-color: #fff;
  outline: none;
  margin-right: 15px;
}
.export-btn {
  background-color: #4caf50;
  color: #fff;
  text-transform: none;
}
.export-btn:hover {
  background-color: black;
}

/* Dropdown Wrapper */
.dropdown-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 110px;
  padding-left: 10px;
}

/* Dropdown Select */
.dropdown {
  width: 100%;
  padding: 5px 28px 5px 10px; /* Reduced padding */
  border-radius: 4px;
  border: 1px solid #d1d5db;
  font-size: 13px; /* Reduced font size */
  height: 32px; /* Explicit height */
  line-height: 1;
  appearance: none;
  cursor: pointer;
  background-color: white;
  color: #111827;
  transition: border-color 0.15s ease-in-out;
}

.dropdown:hover {
  border-color: #9ca3af;
}

.dropdown:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.dropdown:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
}

/* Dropdown Icon */
.dropdown-icon {
  position: absolute;
  right: 8px;
  font-size: 14px;
  color: #6b7280;
  transition: transform 0.2s ease-in-out;
  pointer-events: none;
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

.daterange input {
  color: #000 !important;
  background: #fff !important;
}

/*Mobile*/
@media (max-width: 600px) {
  .chart-container,
  .widget-card {
    padding: 10px !important;
  }

  .widget-actions {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 8px;
  }

  .dropdown-wrapper {
    width: 100% !important;
    margin-bottom: 10px;
  }

  .daterange {
    width: 100% !important;
  }

  .chart-title {
    font-size: 16px !important;
  }

  .widget-title {
    font-size: 18px !important;
  }
}

/*Tablet*/
@media (min-width: 601px) and (max-width: 960px) {
  .widget-actions {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
  }

  .dropdown-wrapper {
    width: 48%;
  }

  .daterange {
    width: 60%;
  }

  .export-btn {
    width: 100%;
  }
}

/*Laptop and Desktop*/
@media (min-width: 961px) {
  .widget-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
  }

  .daterange {
    width: 250px;
  }

  .dropdown-wrapper {
    width: 120px;
  }
}
</style>
