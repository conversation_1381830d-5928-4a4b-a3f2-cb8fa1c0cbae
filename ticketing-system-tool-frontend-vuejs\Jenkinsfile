pipeline {
    agent {
        docker {
            image 'node:22'
            args '-v /var/jen<PERSON>_home/.npm:/root/.npm'
        }
    }
    
    environment {
        // Common environment variables
        NODE_OPTIONS = '--no-warnings'
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Install Dependencies') {
            steps {
                sh 'npm install --legacy-peer-deps'
            }
        }
        
        stage('Lint') {
            steps {
                sh 'npm run lint'
            }
        }
        
        stage('Build') {
            parallel {
                stage('Build Staging') {
                    when {
                        branch 'develop'
                    }
                    steps {
                        // Create staging environment file and build
                        sh '''
                            chmod +x generate-env.sh
                            npm run generate-env:staging
                            npm run build:staging
                        '''
                        // Archive the build artifacts
                        sh 'tar -czf dist-staging.tar.gz dist/'
                        archiveArtifacts artifacts: 'dist-staging.tar.gz', fingerprint: true
                    }
                }
                
                stage('Build Production') {
                    when {
                        branch 'main'
                    }
                    steps {
                        // Create production environment file and build
                        sh '''
                            chmod +x generate-env.sh
                            npm run generate-env:prod
                            npm run build:prod
                        '''
                        // Archive the build artifacts
                        sh 'tar -czf dist-production.tar.gz dist/'
                        archiveArtifacts artifacts: 'dist-production.tar.gz', fingerprint: true
                    }
                }
            }
        }
        
        stage('Deploy') {
            parallel {
                stage('Deploy to Staging') {
                    when {
                        branch 'develop'
                    }
                    steps {
                        // Deploy to staging server
                        sh '''
                            mkdir -p /var/lib/jenkins/workspace/Nex-ticketing-frontend-staging
                            tar -xzf dist-staging.tar.gz -C /var/lib/jenkins/workspace/Nex-ticketing-frontend-staging --strip-components=1
                            echo "Deployed to staging environment"
                        '''
                    }
                }
                
                stage('Deploy to Production') {
                    when {
                        branch 'main'
                    }
                    steps {
                        // Deploy to production server
                        sh '''
                            mkdir -p /var/lib/jenkins/workspace/Nex-ticketing-frontend-production
                            tar -xzf dist-production.tar.gz -C /var/lib/jenkins/workspace/Nex-ticketing-frontend-production --strip-components=1
                            echo "Deployed to production environment"
                        '''
                    }
                }
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            echo 'Build and deployment completed successfully!'
        }
        failure {
            echo 'Build or deployment failed!'
        }
    }
}
