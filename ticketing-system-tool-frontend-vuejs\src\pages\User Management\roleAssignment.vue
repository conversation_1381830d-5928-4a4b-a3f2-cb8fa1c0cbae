<template>
  <div class="page-wrapper page-roleAssignemnt">
    <!--Page Header Section -->
    <div class="page-header">
      <v-row align="center" justify="space-between">
        <v-col cols="auto">
          <h2 class="page-title">
            {{ strings.roleAssignment.title }}
          </h2>
          <v-breadcrumbs :items="items" class="breadcrumbs">
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
      </v-row>
    </div>

    <section class="page-content-wrapper">
      <!-- Empty State -->
    <div v-if="roles.length === 0" class="empty-state">
      {{ strings.roleAssignment.noDataText }}
    </div>

    <!-- User Role Table -->
    <section v-else class="card-list-wrapper pt-3">
      <v-card
        v-for="role in roles"
        :key="role.role_id"
        class="app-primary-card"
      >
        <v-row align="center" justify="space-between">
          <!-- Role Details -->
          <v-col
            v-for="(field, fieldIndex) in [
              { label: 'Role ID', value: role.role_id },
              { label: 'Role Name', value: role.role_name },
            ]"
            :key="fieldIndex"
            cols="5"
            class="user-data-details"
          >
            <div class="user-data-header">
              {{ field.label }}
            </div>
            <div class="user-data-value">
              {{ field.value }}
            </div>
          </v-col>

          <!-- Action Buttons -->
          <v-col cols="2" class="actions">
            <div class="btn-toolbar">
              <v-tooltip location="top">
                <template #activator="{ props }">
                  <v-btn
                    v-bind="props"
                    icon
                    size="x-small"
                    aria-label="Edit Role"
                    class="btn-outlined-secondary"
                    variant="outlined"
                    @click="openEditDialog(role)"
                  >
                    <v-icon>mdi-pencil</v-icon>
                  </v-btn>
                </template>
                {{strings.common.editButton}}
              </v-tooltip>

              <v-tooltip location="top">
                <template #activator="{ props }">
                  <v-btn
                    v-bind="props"
                    icon
                    size="x-small"
                    aria-label="View Role"
                    class="view-btn"
                    variant="outlined"
                    @click="openViewDialog(role)"
                  >
                    <v-icon>mdi-eye</v-icon>
                  </v-btn>
                </template>
                {{strings.common.viewButton}}
              </v-tooltip>
            </div>
          </v-col>
        </v-row>
      </v-card>
    </section>

    <!-- Dialog for Adding or Editing Role -->
    <v-dialog v-model="dialogVisible" max-width="920px">
      <v-card>
        <v-card-title class="d-flex">
          <h5 class="dialog-title">
            {{
              isViewMode ? "View Role" : isEditMode ? "Edit Role" : "Add Role"
            }}
          </h5>
        </v-card-title>
        <v-card-text>
          <v-form ref="roleForm" v-model="isFormValid">
            <v-row>
              <!-- Role ID (Read-Only) -->
              <v-col cols="6">
                <v-text-field
                  v-if="isEditMode || isViewMode"
                  v-model="selectedRole.role_id"
                  label="Role ID"
                  readonly
                  variant="outlined"
                />
              </v-col>
              <!-- Role Name -->
              <v-col :cols="isEditMode || isViewMode ? 6 : 12">
                <v-text-field
                  v-model="selectedRole.role_name"
                  :rules="[roleNameRules]"
                  label="Role Name"
                  :readonly="isEditMode || isViewMode"
                  required
                  variant="outlined"
                />
              </v-col>
            </v-row>

            <!-- Module Access -->
            <v-row v-if="userModules.length > 0">
              <v-col cols="12">
                <v-expansion-panels>
                  <v-expansion-panel>
                    <v-expansion-panel-title>
                      <h3>{{ strings.roleAssignment.moduleAccessText }}</h3>
                    </v-expansion-panel-title>
                    <v-expansion-panel-text>
                      <ul class="dialog-custom-list-group role-mgmt-list">
                        <li v-for="(module, index) in userModules" :key="index">
                          <v-row
                            align="center"
                            justify="space-between"
                            class="module-access-container"
                          >
                            <!-- Module Name -->
                            <v-col cols="auto">
                              <div class="module-name">
                                <h6>{{ module.module_name }}</h6>
                              </div>
                            </v-col>

                            <!-- Checkbox -->
                            <v-col cols="auto">
                              <v-checkbox
                                v-model="module.has_access"
                                class="custom-checkbox"
                                :readonly="isViewMode"
                                :disabled="module.module_name === 'Dashboard'"
                                @change="handleModuleCheckboxChange(module)"
                              />
                            </v-col>

                            <!-- SubModules -->
                            <v-col
                              v-if="module.sub_modules.length > 0"
                              cols="12"
                            >
                              <v-select
                                v-model="module.selectedSubModules"
                                :items="module.sub_modules"
                                item-title="submodule_name"
                                item-value="submodule_id"
                                label="Select SubModules"
                                multiple
                                clearable
                                chips
                                :readonly="isViewMode"
                                closable-chips
                                variant="outlined"
                                :disabled="!module.has_access"
                                @update:model-value="
                                  handleSubModuleSelection(module)
                                "
                              />
                            </v-col>
                          </v-row>
                        </li>
                      </ul>
                    </v-expansion-panel-text>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-btn variant="text" @click="closeDialog"> {{strings.common.closeButton}} </v-btn>
          <v-btn
            v-if="!isViewMode && isEditMode"
            variant="tonal"
            :disabled="!isFormModified"
            @click="validateAndUpdate"
          >
            {{ strings.roleAssignment.buttons.update }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    </section>

    
  </div>
</template>

<script lang="ts">
import { useToast } from "vue-toastification";
import {
  createRole,
  getUserModuleAccess,
  getUserRoles,
  updateUserModuleAccess,
} from "../../api/apiClient";
import { computed, defineComponent, onMounted, ref } from "vue";
import strings from "../../assets/strings.json";

interface Role {
  role_id: string;
  role_name: string;
}

interface Module {
  module_id: number;
  module_name: string;
  has_access: boolean;
  sub_modules: SubModule[]; // Ensure this exists
  selectedSubModules: number[]; // Track selected submodules
}

interface SubModule {
  submodule_id: number;
  submodule_name: string;
  has_access: boolean;
}

export default defineComponent({
  name: "RoleAssignment",
  setup() {
    const toast = useToast();
    const items = ref([
      { title: "Home", href: "/" },
      { title: "User Management", href: "/manage-users" },
      { title: "Project Mapping", href: "" },
    ]);

    const handleCheckboxChange = (module: any) => {
      if (!module.has_access) {
        module.selectedPermissions = []; // Reset selection when disabled
      }
    };

    const dialogVisible = ref(false);
    const isFormValid = ref(false);
    const roleName = ref("");
    const roles = ref<Role[]>([]);
    const userModules = ref<Module[]>([]);
    const isLoading = ref(true);
    const selectedRole = ref<Role>({ role_id: "", role_name: "" });
    const isEditMode = ref(false);
    const isViewMode = ref(false);
    const originalRole = ref<Role | null>(null);
    const originalModules = ref<Module[]>([]);
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const roleNameRules = (value: string) =>
      value.trim() !== "" || "Role Name is required";

    const isFormModified = computed(() => {
      if (!originalRole.value) return false;

      // Compare role name changes
      const roleChanged =
        selectedRole.value.role_name !== originalRole.value.role_name;

      // Compare module & submodule access changes
      const modulesChanged =
        JSON.stringify(userModules.value) !==
        JSON.stringify(originalModules.value);

      return roleChanged || modulesChanged;
    });

    const openEditDialog = async (role: Role) => {
      try {
        isEditMode.value = true;
        isViewMode.value = false; // Ensure view mode is false
        selectedRole.value = { ...role };
        dialogVisible.value = true;

        const response = await getUserModuleAccess(role.role_id);
        if (response.data && response.data.length > 0) {
          userModules.value = response.data[0].modules.map((mod: any) => ({
            module_id: mod.module_id,
            module_name: mod.module_name,
            has_access: mod.has_access,
            sub_modules: mod.sub_module
              ? mod.sub_module.map((sub: any) => ({
                  submodule_id: sub.submodule_id,
                  submodule_name: sub.submodule_name,
                  has_access: sub.has_access,
                }))
              : [],
            selectedSubModules: mod.sub_module
              ? mod.sub_module
                  .filter((sub: any) => sub.has_access)
                  .map((sub: any) => sub.submodule_id)
              : [],
          }));
        } else {
          userModules.value = [];
        }
        originalRole.value = { ...selectedRole.value };
        originalModules.value = JSON.parse(JSON.stringify(userModules.value));
      } catch (error: any) {
        console.error("Error fetching user module access: ", error);
        toast.error("Failed to fetch user module access. Please try again.");
      }
    };

    const openViewDialog = async (role: Role) => {
      try {
        isEditMode.value = false; // Ensure edit mode is false
        isViewMode.value = true; // Enable view mode
        selectedRole.value = { ...role };
        dialogVisible.value = true;

        const response = await getUserModuleAccess(role.role_id);
        if (response.data && response.data.length > 0) {
          userModules.value = response.data[0].modules.map((mod: any) => ({
            module_id: mod.module_id,
            module_name: mod.module_name,
            has_access: mod.has_access,
            sub_modules: mod.sub_module
              ? mod.sub_module.map((sub: any) => ({
                  submodule_id: sub.submodule_id,
                  submodule_name: sub.submodule_name,
                  has_access: sub.has_access,
                }))
              : [],
            selectedSubModules: mod.sub_module
              ? mod.sub_module
                  .filter((sub: any) => sub.has_access)
                  .map((sub: any) => sub.submodule_id)
              : [],
          }));
        } else {
          userModules.value = [];
        }
      } catch (error: any) {
        console.error("Error fetching user module access: ", error);
        toast.error("Failed to fetch user module access. Please try again.");
      }
    };

    const validateAndUpdate = () => {
      let isValid = true;

      userModules.value.forEach((module) => {
        if (
          module.has_access && // If module is checked
          module.sub_modules.length > 0 && // If it has submodules
          module.selectedSubModules.length === 0 // If no submodules are selected
        ) {
          isValid = false;
          toast.error(
            `Please select at least one submodule for "${module.module_name}".`
          );
        }
      });

      if (!isValid) {
        return;
      }

      // If validation passes, proceed to update the module access
      handleUpdateModuleAccess();
    };

    // Handle Module Checkbox Change
    const handleModuleCheckboxChange = (module: Module) => {
      if (!module.has_access) {
        module.selectedSubModules = []; // Reset submodule selection if module is unchecked
      }
    };

    // Handle SubModule Selection
    const handleSubModuleSelection = (module: Module) => {};

    const closeDialog = () => {
      dialogVisible.value = false;
    };

    const handleUpdateModuleAccess = async () => {
      try {
        // Filter modules with access and map their IDs
        const modulesWithAccess = userModules.value
          .filter((module) => module.has_access)
          .map((module) => module.module_id);

        // Collect submodules that have access
        const subModulesWithAccess = userModules.value.reduce((acc, module) => {
          return acc.concat(module.selectedSubModules);
        }, [] as number[]);

        const updatedBy = user.id;

        // Validate the payload
        if (modulesWithAccess.length === 0) {
          toast.error("No modules selected for access.");
          return;
        }

        // Call the API
        const response = await updateUserModuleAccess(
          selectedRole.value.role_id,
          {
            modulesWithAccess,
            subModulesWithAccess,
            updatedBy,
          }
        );
        if (response.message) {
          const successMessage = response?.message;
          toast.success(successMessage);
        } else {
          toast.error(response.error);
        }
        window.location.reload();
        closeDialog();
      } catch (error: unknown) {
        console.error("Error updating module and submodule access:", error);
      }
    };

    // Fetch roles helper function
    const fetchRoles = async () => {
      try {
        isLoading.value = true;
        const response = await getUserRoles();
        roles.value = response.data || [];
      } catch (error: any) {
        console.error("Error fetching roles:", error);
        toast.error("Failed to load roles. Please try again.");
      } finally {
        isLoading.value = false;
      }
    };

    onMounted(async () => {
      await fetchRoles();
    });

    return {
      items,
      dialogVisible,
      selectedRole,
      isFormValid,
      roles,
      isEditMode,
      isViewMode,
      userModules,
      strings,
      roleNameRules,
      openEditDialog,
      openViewDialog,
      closeDialog,
      handleUpdateModuleAccess,
      handleCheckboxChange,
      handleModuleCheckboxChange,
      handleSubModuleSelection,
      validateAndUpdate,
      isFormModified,
    };
  },
});
</script>

<style scoped>
.add-user-header {
  margin-bottom: 20px;
}

.breadcrumbs {
  font-size: 14px;
  color: grey;
}
.actions {
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
.empty-state {
  text-align: center;
  color: grey;
  font-size: 16px;
  margin-top: 20px;
}
.remove-btn {
  border: 1px solid red;
  color: red;
}
.remove-btn:hover {
  background-color: red;
  color: #fff;
}
.edit-btn {
  border: 1px solid #026bb1;
  color: #026bb1;
}
.edit-btn:hover {
  background-color: #026bb1;
  color: #fff;
}
.view-btn {
  border: 1px solid #2dcc70;
  color: #2dcc70;
}
.view-btn:hover {
  background-color: #2dcc70;
  color: white;
}
</style>
