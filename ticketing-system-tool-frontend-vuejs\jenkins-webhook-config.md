# Jenkins Webhook Configuration for GitLab

This document explains how to set up a webhook in GitLab to trigger <PERSON> builds automatically when code is pushed to specific branches.

## Setting Up Jenkins

1. Install the GitLab Plugin in Jenkins:
   - Go to <PERSON> > <PERSON><PERSON> > Plugins > Available
   - Search for "GitLab"
   - Install the "GitLab Plugin"

2. Configure <PERSON> to accept GitLab webhooks:
   - Go to <PERSON> > <PERSON><PERSON> > Configure System
   - Scroll down to the GitLab section
   - Add a new GitLab connection with a name (e.g., "GitLab Connection")
   - Generate an API token and save it

3. Create a Jenkins Pipeline job:
   - Create a new Pipeline job
   - In the Pipeline section, select "Pipeline script from SCM"
   - Select Git as the SCM
   - Enter your repository URL
   - Specify the branches to build (e.g., `*/develop`, `*/main`)
   - Set the Script Path to "Jenkinsfile"

4. Configure Build Triggers:
   - In the job configuration, check "Build when a change is pushed to Git<PERSON>ab"
   - Select the GitLab connection you created
   - Save the job

## Setting Up GitLab Webhook

1. Get the <PERSON> webhook URL:
   - In your Jenkins job, click on "Git<PERSON>ab" in the left sidebar
   - <PERSON>py the "GitLab webhook URL"

2. Add the webhook in GitLab:
   - Go to your GitLab project > Settings > Webhooks
   - Paste the Jenkins webhook URL
   - Select the events to trigger the webhook (Push events, Merge Request events)
   - Add the webhook

## Environment-Specific Builds

The Jenkinsfile is configured to build different environments based on the branch:

- **develop branch**: Builds with staging configuration
  - API URL: `http://internal-project.nexware-global.com:9019/api/`
  - WebSocket URL: `ws://localhost:8000/ws/ticket/`

- **main branch**: Builds with production configuration
  - API URL: `https://ticket.nexware-global.com:9049/api/`
  - WebSocket URL: `wss://internal-project.nexware-global.com:8001/ws/ticket/`

## Troubleshooting

If the webhook is not triggering builds:

1. Check the GitLab webhook logs in your GitLab project > Settings > Webhooks
2. Ensure the Jenkins URL is accessible from GitLab
3. Verify that the Jenkins job is configured to accept webhooks
4. Check the Jenkins system log for any errors

## Manual Triggering

You can also manually trigger builds by:

1. Going to your Jenkins job
2. Clicking "Build Now"
3. Selecting the branch to build
