let socket: WebSocket | null = null;
export const socketConnected = ref(false);
interface Message {
  timestamp: string;
  sender_id: number;
  // sender_name: string;
  message: string;
  file_name?: string;
  file_data?: string;
  attachement?: { file_data: string; file_name: string }[];
}

export const connectSocket = (
  roomName: number,
  onMessage: (message: Message) => void
) => {
  try {
    // Use the WebSocket URL from environment variables or fall back to a default
    const wsBaseUrl =
      import.meta.env.VITE_WS_URL || "ws://localhost:8000/ws/ticket/";
    socket = new WebSocket(`${wsBaseUrl}${roomName}/`);

    socket.onopen = () => {
      console.log("✅ WebSocket Connected");
      socketConnected.value = true;
    };

    socket.onmessage = (event) => {
      try {
        console.log("📩 Message Received:", event.data);
        const data = JSON.parse(event.data);
        console.log("Parsed Data:", data);

        // Normalize attachement array
        const attachement = Array.isArray(data.attachement)
          ? data.attachement
          : [];

        onMessage({
          sender_id: data.sender_id,
          message: data.message,
          attachement, // Pass the whole array to your message handler
          timestamp: new Date().toISOString(),
        });
      } catch (parseError) {
        console.error("❗ Failed to parse WebSocket message:", parseError);
      }
    };

    socket.onerror = (error) => {
      console.error("❗ WebSocket Error:", error);
      socketConnected.value = false;
    };

    socket.onclose = (event) => {
      console.warn(
        `❌ WebSocket Disconnected: ${event.reason} (Code: ${event.code})`
      );
      // Auto-reconnect
      setTimeout(() => {
        console.log("🔄 Reconnecting...");
        connectSocket(roomName, onMessage);
      }, 3000);
      socketConnected.value = false;
    };
  } catch (error: any) {
    console.error(
      "❗ Failed to establish WebSocket connection:",
      error.message
    );
  }
};

export const sendMessage = (data: {
  sender_id: number;
  message: string;
  attachement: { file_data: string; file_name: string }[];
}) => {
  try {
    if (socket instanceof WebSocket && socket.readyState === WebSocket.OPEN) {
      socket.send(
        JSON.stringify({
          sender_id: data.sender_id,
          message: data.message,
          attachement: data.attachement, // FIXED KEY
        })
      );
      console.log("📤 Message Sent:", data);
    } else {
      throw new Error("WebSocket is not connected");
    }
  } catch (error) {
    console.error("❗ Failed to send message:", error);
  }
};

export const disconnectSocket = () => {
  try {
    if (socket instanceof WebSocket) {
      socket.close();
      socket = null; // Reset socket after closing
      console.log("🔌 WebSocket Disconnected manually");
    }
  } catch (error) {
    console.error("❗ Failed to disconnect WebSocket:", error);
  }
};

let logoutSocket: WebSocket | null = null;
let reconnectTimeout: ReturnType<typeof setTimeout> | null = null;
let isManuallyClosed = false;

export const connectLogoutSocket = (userId: number) => {
  // Prevent reconnection if socket is already open or connecting
  if (
    logoutSocket &&
    (logoutSocket.readyState === WebSocket.OPEN ||
      logoutSocket.readyState === WebSocket.CONNECTING)
  ) {
    console.log("🚫 WebSocket already open or connecting");
    return;
  }

  if (logoutSocket) {
    logoutSocket.close();
    logoutSocket = null;
  }

  isManuallyClosed = false;

  const wsBaseUrl =
    import.meta.env.VITE_WS_LOGOUT_URL ||
    "ws://localhost:8000/ws/force-logout/";
  const url = `${wsBaseUrl}${userId}/`;

  logoutSocket = new WebSocket(`ws://localhost:8000/ws/force-logout/${userId}/`);

  logoutSocket.onopen = () => {
    console.log("🟢 Logout WebSocket connected");
    socketConnected.value = true;
  };

  logoutSocket.onmessage = async (event) => {
    console.log("📨 Logout socket message:", event.data);
    try {
      const data = JSON.parse(event.data);
      if (data.action === "logout") {
        console.warn("⚠️ Forced logout received from server");
        localStorage.clear();
        alert("Your session has expired due to a role change.");
        window.location.href = "/login";
      }
    } catch (error) {
      console.error("Error processing logout message:", error);
    }
  };

  logoutSocket.onerror = (err) => {
    console.error("❌ Logout WebSocket error:", err);
    socketConnected.value = false;

    if (!isManuallyClosed && !reconnectTimeout) {
      reconnectTimeout = setTimeout(() => {
        reconnectTimeout = null;
        connectLogoutSocket(userId);
      }, 5000);
    }
  };

  logoutSocket.onclose = (event) => {
    console.warn(
      `🔌 Logout WebSocket disconnected: ${event.reason} (Code: ${event.code})`
    );
    socketConnected.value = false;

    if (!isManuallyClosed && !reconnectTimeout) {
      reconnectTimeout = setTimeout(() => {
        reconnectTimeout = null;
        connectLogoutSocket(userId);
      }, 3000);
    }
  };
};

export const disconnectLogoutSocket = () => {
  isManuallyClosed = true;

  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }

  if (logoutSocket) {
    const socket = logoutSocket;
    logoutSocket = null;
    socket.close();
    console.log("🔌 Logout WebSocket closed manually");
  }
};

