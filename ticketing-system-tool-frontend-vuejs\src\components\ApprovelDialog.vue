<template>
  <v-dialog
    v-model="isVisible"
    max-width="500"
  >
    <v-card>
      <v-card-title>
        <div class="d-flex align-center justify-space-between">
          <h5>{{strings.common.ticketApproval}}</h5>
          <v-btn
            icon
            class="dialog-close-btn"
            @click="closeDialog"
          >
            <v-icon icon="mdi-close" />
          </v-btn>
        </div>
      </v-card-title>

      <v-card-text>
        <v-row dense>
          <!-- Approval Status -->
          <v-col cols="12">
            <v-select
              v-model="formData.approvel_status"
              :v-error-messages="v$.approvel_status.$errors.map((e) => String(e.$message))"
              :items="statusOptions"
              label="Approval Status"
              variant="outlined"
              required
              @blur="v$.approvel_status.$touch"
              @change="v$.approvel_status.$touch"
            />
          </v-col>

          <!-- Approval Message -->
          <v-col cols="12">
            <v-textarea
              v-model="formData.approvel_message"
              :v-error-messages="v$.approvel_message.$errors.map((e) => String(e.$message))"
              label="Message"
              variant="outlined"
              required
              @blur="v$.approvel_message.$touch"
            />
          </v-col>

          <!-- File Upload -->
          <v-col cols="12">
            <v-file-input
              v-model="formData.attachment"
              :v-error-messages="v$.attachment.$errors.map((e) => String(e.$message))"
              label="Upload Documents"
              variant="outlined"
              required
              multiple
              show-size
              prepend-icon="mdi-upload"
              accept=".pdf,.doc,.docx,.png,.jpg"
              @blur="v$.attachment.$touch"
            />
          </v-col>
        </v-row>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn
          variant="tonal"
          @click="closeDialog"
        >
          {{strings.common.cancelButton}}
        </v-btn>
        <v-btn
          color="primary"
          @click="submitForm"
        >
          {{strings.common.submitButton}}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch } from "vue";
import useVuelidate from "@vuelidate/core";
import { required } from "@vuelidate/validators";
import strings from "../assets/strings.json";

// Export the Props interface to make it available for other components
export interface ApprovelDialog {
  modelValue: boolean;
  statusOptions: { text: string; value: string }[];
}

// Export FormData interface as well
export interface FormData {
  approvel_status: string;
  approvel_message: string;
  attachment: File[] | null;
}

// Define the props passed into the component
const props = defineProps<ApprovelDialog>();

// Define the emits
const emit = defineEmits<{
  (e: "update:modelValue", value: boolean): void;
  (e: "submit", data: FormData): void;
}>();

// Dialog visibility state
const isVisible = ref<boolean>(props.modelValue);

// Form data state
const formData = ref<FormData>({
  approvel_status: "",
  approvel_message: "",
  attachment: [] as File[],
  // attachment: null,
});

// Validation rules for the form fields
const rules = {
  approvel_status: { required },
  approvel_message: { required },
  attachment: { required },
};

// Setup Vuelidate
const v$ = useVuelidate(rules, formData);

// Watch for changes to `modelValue` prop
watch(
  () => props.modelValue,
  (newValue) => {
    isVisible.value = newValue;
  }
);

// Close the dialog and emit `update:modelValue` event
const closeDialog = () => {
  emit("update:modelValue", false);
};

// Submit the form, validate it, and emit the `submit` event
const submitForm = async () => {
  const isValid = await v$.value.$validate();
  if (!isValid) return;

  emit("submit", formData.value);
  closeDialog();
};
</script>
