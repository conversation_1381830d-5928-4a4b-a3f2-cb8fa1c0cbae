<!-- TextEditor.vue -->
<template>
  <div class="editor-container">
    <textarea
      v-if="!quillLoaded"
      v-model="content"
      class="fallback-textarea"
      :placeholder="strings.textEditor.placeholder"
    ></textarea>
    <div v-else ref="quillContainer" class="quill-editor"></div>
  </div>
</template>

<script lang="ts">
import { ref, defineComponent, onMounted, onBeforeUnmount } from "vue";
import type Quill from "quill";
import strings from "../assets/strings.json";
// Import Quill directly instead of through vue3-quill
// We'll initialize it manually for more control

export default defineComponent({
  name: "TextEditor",
  props: {
    initialContent: {
      type: String,
      default: "",
    },
  },
  emits: ["update:content", "ready"],
  setup(props, { emit }) {
    const quillContainer = ref(null);
    const content = ref(props.initialContent);
    const quillInstance = ref<Quill | null>(null);
    const quillLoaded = ref(false);

    onMounted(async () => {
      try {
        // Dynamically import Quill to avoid SSR issues
        const Quill = await import("quill").then(
          (module) => module.default || module
        );

        // Register alignment module
        const { AlignClass } = await import("quill/formats/align");
        Quill.register(AlignClass, true);

        quillLoaded.value = true;

        // Wait for next tick to ensure container is in the DOM
        setTimeout(() => {
          if (quillContainer.value) {
            // Initialize Quill
            quillInstance.value = new Quill(quillContainer.value, {
              theme: "snow",
              placeholder: "Type your message here...",
              modules: {
                toolbar: [
                  ["bold", "italic", "underline", "strike"],
                  [{ list: "ordered" }],
                  [{ script: "sub" }, { script: "super" }],
                  [{ color: [] }, { background: [] }],
                ],
              },
              formats: [
                "bold",
                "italic",
                "underline",
                "strike",
                "list",
                "color",
                "script",
                "background",
              ],
            });

            // Set initial content if provided
            if (props.initialContent) {
              quillInstance.value.clipboard.dangerouslyPasteHTML(
                props.initialContent
              );
            }

            // Set default alignment to left for all new content
            quillInstance.value.format("align", "justify");

            // Set up content change handler
            let lastHtml = "";

            quillInstance.value.on("text-change", () => {
              let html = quillInstance.value!.root.innerHTML;

              if (html !== lastHtml) {
                lastHtml = html;
                emit("update:content", html);
              }
            });

            // Emit ready event
            emit("ready", quillInstance.value);
          }
        }, 0);
      } catch (error) {
        console.error("Failed to load Quill editor:", error);
        quillLoaded.value = false;
      }
    });

    onBeforeUnmount(() => {
      // Clean up
      if (quillInstance.value) {
        quillInstance.value = null;
      }
    });

    const getContent = () => {
      if (quillInstance.value) {
        // Return innerHTML without replacing <ol> with <ul>
        let html = quillInstance.value.root.innerHTML;
        return html;
      }
      return content.value;
    };

    const clearContent = () => {
      if (quillInstance.value) {
        // Clear Quill's content properly
        quillInstance.value.setContents([{ insert: "\n" }]);
      }
    };

    return {
      quillContainer,
      content,
      quillLoaded,
      getContent,
      clearContent,
      strings,
    };
  },
});
</script>

<style scoped>
.editor-container {
  border-radius: 4px;
  width: 100%;
}

.fallback-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
}

.quill-editor {
  height: 150px;
  border-radius: 4px;
}

:deep(.ql-toolbar) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  background: #f8f9fa;
  border-color: #ced4da;
}

:deep(.ql-container) {
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  min-height: 120px;
  max-height: 300px;
  overflow-y: auto;
  border-color: #ced4da;
  font-family: inherit;
}

:deep(.ql-editor) {
  font-size: 16px;
  line-height: 1.5;
  min-height: 120px;
  text-align: left;
  padding-top: 8px;
  vertical-align: top;
}

:deep(.ql-editor p) {
  margin: 0;
  text-align: left;
}

:deep(.ql-editor.ql-blank::before) {
  color: #6c757d;
  font-style: italic;
}
</style>
