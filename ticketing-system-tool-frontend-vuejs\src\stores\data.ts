// Interface for a single project detail
export interface ProjectDetail {
  project_id: number;
  project_name: string;
}

// Interface for the user object
export interface User {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  role_name: string;
  location_name: string;
  email: string;
  is_active: boolean;
  is_deleted: boolean;
  profile_pic: string | null;
  phone_number: string;
  projectDetails: ProjectDetail[]; // Array of projects
}

// Interface for the API response
export interface UserResponse {
  data: User;
}


export interface Category {
  id?: number;
  cat_name: string;
}

export interface SubCategory {
  id?: number;
  subcat_name: string;
  category: number; // Reference to Category ID
}

export interface Ticket {
  ticket_id: number;
  title: string;
  description: string;
  category: number;
  subcategory: number;
  documents: [];
  project: string;
  status: string;
  urgency: string;
  location: string;
  created_at: Date;
  updated_at: Date;
  closed_at: number;
  solved_at: number;
  assigned_to_fullname: string;
  created_by_fullname: string;
  assigned_to?: {
    first_name: string;
    email: string;
    [key: string]: any;
  };
  created_by: number; // Add this if not present
  watchers: string;
  createdByUser?: {
    first_name: string;
    email: string;
    [key: string]: any;
  };
}
