image: node:22 # Use Node.js 22 image

stages:
  - test
  - build
  - deploy

variables:
  REMOTE_USER: "dev-nex"
  REMOTE_HOST: "***************" # On-premises server IP
  REMOTE_PATH: "/var/lib/jenkins/workspace/Nex-ticketing-frontend" # Destination folder on the server

# ✅ Linting & Unit Tests
lint-test-job:
  stage: test
  script:
    - echo "📦 Installing dependencies for linting and testing..."
    - npm ci --legacy-peer-deps # Clean install for better reproducibility
    - echo "🔍 Checking installed dependencies..."
    - npm list @vue/test-utils
    - echo "✅ Running ESLint..."
    - npm run lint
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"

# ✅ Build Vue.js Project for Staging
build-staging-job:
  stage: build
  script:
    - echo "📦 Installing dependencies..."
    - npm install --legacy-peer-deps
    - echo "🔧 Creating environment file for staging..."
    - echo "VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/" > .env.staging
    - echo "VITE_WS_URL=ws://localhost:8000/ws/ticket/" >> .env.staging
    - echo "⚙️ Building Vue.js project for staging..."
    - npm run build:staging
    - echo "🗜️ Zipping build files..."
    - zip -r build.zip dist/ # Create build.zip
  artifacts:
    paths:
      - dist/ # Store the build files
      - build.zip # Ensure build.zip is available for deployment
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"

# ✅ Build Vue.js Project for Production
build-production-job:
  stage: build
  script:
    - echo "📦 Installing dependencies..."
    - npm install --legacy-peer-deps
    - echo "🔧 Creating environment file for production..."
    - echo "VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/" > .env.production
    - echo "VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/" >> .env.production
    - echo "⚙️ Building Vue.js project for production..."
    - npm run build:prod
    - echo "🗜️ Zipping build files..."
    - zip -r build.zip dist/ # Create build.zip
  artifacts:
    paths:
      - dist/ # Store the build files
      - build.zip # Ensure build.zip is available for deployment
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"

# ✅ Upload & Deploy Staging Build to On-Prem Server
upload-staging-job:
  stage: deploy
  needs: ["build-staging-job"]
  variables:
    REMOTE_PATH: "/var/lib/jenkins/workspace/Nex-ticketing-frontend-staging"
  before_script:
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $REMOTE_HOST >> ~/.ssh/known_hosts

  script:
    - echo "📂 Checking build.zip existence..."
    - '[ -f "build.zip" ] || (echo "❌ Error: build.zip not found! Exiting..."; exit 1)'

    - echo "📤 Uploading staging build to on-premises server..."
    - scp build.zip $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/build.zip

    - echo "🗄️ Extracting build.zip on server..."
    - ssh $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && unzip -o build.zip && rm build.zip"

    - echo "✅ Staging deployment completed successfully!"

  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'

# ✅ Upload & Deploy Production Build to On-Prem Server
upload-production-job:
  stage: deploy
  needs: ["build-production-job"]
  variables:
    REMOTE_PATH: "/var/lib/jenkins/workspace/Nex-ticketing-frontend-production"
  before_script:
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - ssh-keyscan -H $REMOTE_HOST >> ~/.ssh/known_hosts

  script:
    - echo "📂 Checking build.zip existence..."
    - '[ -f "build.zip" ] || (echo "❌ Error: build.zip not found! Exiting..."; exit 1)'

    - echo "📤 Uploading production build to on-premises server..."
    - scp build.zip $REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/build.zip

    - echo "🗄️ Extracting build.zip on server..."
    - ssh $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_PATH && unzip -o build.zip && rm build.zip"

    - echo "✅ Production deployment completed successfully!"

  rules:
    - if: '$CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master"'
    
