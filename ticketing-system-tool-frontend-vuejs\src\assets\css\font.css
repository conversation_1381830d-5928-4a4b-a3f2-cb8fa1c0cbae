/* Light */
@font-face {
  font-family: "Barlow";
  src: url("../fonts/Barlow-Light.woff2") format("woff2"),
    url("../fonts/Barlow-Light.ttf") format("truetype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
/* Regular-400*/
@font-face {
  font-family: "Barlow";
  src: url("../fonts/Barlow-Regular.woff2") format("woff2"),
    url("../fonts/Barlow-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
/* Medium-500 */
@font-face {
  font-family: "Barlow";
  src: url("../fonts/Barlow-Medium.woff2") format("woff2"),
    url("../fonts/Barlow-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
/* SemiBold-600*/
@font-face {
  font-family: "Barlow";
  src: url("../fonts/Barlow-SemiBold.woff2") format("woff2"),
    url("../fonts/Barlow-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
/* Bold-700 */
@font-face {
  font-family: "Barlow";
  src: url("../fonts/Barlow-Bold.woff2") format("woff2"),
    url("../fonts/Barlow-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}
/* Extra Bold */
@font-face {
  font-family: "Barlow";
  src: url("../fonts/Barlow-ExtraBold.woff2") format("woff2"),
    url("../fonts/Barlow-ExtraBold.ttf") format("truetype");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}
