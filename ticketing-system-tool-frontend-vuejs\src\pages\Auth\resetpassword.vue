<template>
  <div class="app-layout">
    <section class="app-login-wrapper">
      <div class="left">
        <img src="../../assets/images/login-image.png" alt="image" />
      </div>
      <div class="right">
        <div class="logo">
          <img src="../../assets/icons/logo.svg" alt="image" />
        </div>

        <div v-if="linkExpired" class="expired-message">
          <h2>Reset Link Expired</h2>
          <p>{{ expiredMessage }}</p>
          <div class="btn-toolbar">
            <router-link to="/forgotpassword" class="text-decoration-none ">
              <v-btn variant="tonal" block>
                {{ strings.resetPassword.backToForgetpassowrd }}
              </v-btn>
            </router-link>
          </div>
        </div>
        <div v-else>
          <h2>{{ strings.resetPassword.title }}</h2>
          <p>{{ strings.resetPassword.displaymessage }}</p>

          <v-form ref="form" @submit.prevent="validateAndResetPassword">
            <!-- Password Field -->
            <v-text-field
              v-model="password"
              :placeholder="strings.resetPassword.passwordPlaceholder"
              :error="passwordErrors.length > 0"
              :error-messages="passwordErrors"
              :type="passwordVisible ? 'text' : 'password'"
              :append-inner-icon="passwordVisible ? 'mdi-eye' : 'mdi-eye-off'"
              prepend-inner-icon="mdi-lock-outline"
              density="compact"
              variant="outlined"
              class="mb-3"
              @click:append-inner="passwordVisible = !passwordVisible"
            />

            <!-- Confirm Password Field -->
            <v-text-field
              v-model="confirmPassword"
              :placeholder="strings.resetPassword.confirmPasswordPlaceholder"
              :error="confirmPasswordErrors.length > 0"
              :error-messages="confirmPasswordErrors"
              :type="confirmPasswordVisible ? 'text' : 'password'"
              :append-inner-icon="
                confirmPasswordVisible ? 'mdi-eye' : 'mdi-eye-off'
              "
              prepend-inner-icon="mdi-lock-outline"
              density="compact"
              variant="outlined"
              class="mb-3"
              @click:append-inner="
                confirmPasswordVisible = !confirmPasswordVisible
              "
            />

            <!-- Reset Password Button -->
            <div class="btn-toolbar">
              <v-btn
                variant="tonal"
                block
                :loading="loading"
                :disabled="loading"
                type="submit"
              >
                {{ strings.resetPassword.buttonMessage }}
              </v-btn>
            </div>
          </v-form>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import strings from "../../../src/assets/strings.json";
import { useToast } from "vue-toastification";
import type { VForm } from "vuetify/components";
import { resetPassword, validateResetToken } from "../../api/apiClient";

export default defineComponent({
  setup() {
    const password = ref<string>("");

    const confirmPassword = ref<string>("");

    const passwordVisible = ref<boolean>(false);

    const confirmPasswordVisible = ref<boolean>(false);

    const loading = ref<boolean>(false);

    const token = ref<string>("");

    const form = ref<any>(null);
    const expiredMessage = ref("");
    const submitAttempted = ref(false);
    const linkExpired = ref(false);

    const router = useRouter();
    const route = useRoute();
    const toast = useToast();

    // const fromInternalNavigation = computed(
    //   () => route.query.internal === "true"
    // );

    const passwordErrors = computed(() => {
      if (!submitAttempted.value) return [];

      const errors: string[] = [];
      if (!password.value) {
        errors.push("Password is required");
      } else {
        if (!/[A-Z]/.test(password.value))
          errors.push("At least one uppercase letter required");
        if (!/[a-z]/.test(password.value))
          errors.push("At least one lowercase letter required");
        if (!/\d/.test(password.value))
          errors.push("At least one digit required");
        if (!/[^A-Za-z0-9]/.test(password.value))
          errors.push("At least one special character required");
        if (password.value.length < 8)
          errors.push("Password must be at least 8 characters");
      }

      return errors;
    });

    const confirmPasswordErrors = computed(() => {
      if (!submitAttempted.value) return [];

      const errors: string[] = [];
      if (!confirmPassword.value) {
        errors.push("*Confirm password is required");
      } else if (confirmPassword.value !== password.value) {
        errors.push("*Passwords do not match");
      }

      return errors;
    });

    const userId = ref("");

    const getToken = async () => {
      const params = route.params as { user_id?: string; token?: string };
      userId.value = params.user_id ?? "";
      token.value = params.token ?? "";

      const exp = route.query.exp;
      const isNewUser = route.query.new_user === "true";

      if (!isNewUser && exp) {
        const expString = Array.isArray(exp) ? exp[0] : exp;
        const expTime = new Date(Number(expString) * 1000);
        const currentTime = new Date();

        if (currentTime > expTime) {
          linkExpired.value = true;
          expiredMessage.value =
            "This link has expired. Please request a new one.";
        }

        // ✅ Call backend to check token status
        try {
          await validateResetToken({
            token: token.value,
            user_id: userId.value,
          });
        } catch (error: any) {
          linkExpired.value = true;
          expiredMessage.value =
            error.response?.data?.error || "Invalid reset link.";
        }
      }
    };

    const validateAndResetPassword = async () => {
      submitAttempted.value = true;

      if (linkExpired.value) {
        toast.error("This reset link has expired.");
        return;
      }

      if (passwordErrors.value.length || confirmPasswordErrors.value.length) {
        toast.error("Please fix the form errors before continuing.");
        return;
      }

      loading.value = true;
      try {
        const payload: Record<string, string | number> = {
          password: confirmPassword.value,
          new_user: 0,
          token: token.value,
        };

        const response = await resetPassword(payload);

        if (response?.message) {
          toast.success(response.message);
          localStorage.clear();
          setTimeout(() => router.push("/login"), 2000);
        }
      } catch (error: any) {
        console.error("Reset Password Error:", error);
        const errorMessage = error.response?.data?.error;
        if (errorMessage) {
          toast.error(errorMessage);
        }
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      getToken();
    });

    watch(
      () => route.fullPath,
      () => getToken()
    );

    return {
      password,
      confirmPassword,
      passwordVisible,
      confirmPasswordVisible,
      loading,
      validateAndResetPassword,
      strings,
      passwordErrors,
      confirmPasswordErrors,
      form,
      linkExpired,
      expiredMessage,
      submitAttempted,
      // fromInternalNavigation,
    };
  },
});
</script>

<style scoped>
.app-login-wrapper {
  display: flex;
  min-height: 100vh;
}

.left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.right {
  flex: 1;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.centered-reset {
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
  display: flex;
  background-image: none;
}

.centered-reset .right {
  max-width: 400px;
  width: 100%;
}

.centered-reset .left {
  display: none;
}

.centered-reset .logo {
  display: none;
}



.expired-message h2 {
  color: #e53935;
  font-size: 24px;
  margin-bottom: 16px;
}

.expired-message p {
  color: #555;
  font-size: 16px;
  margin-bottom: 24px;
}

.btn-toolbar {
  display: flex;
  justify-content: center;
}



</style>
