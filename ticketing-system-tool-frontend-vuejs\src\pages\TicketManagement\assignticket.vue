<template>
  <div class="page-wrapper page-assign-ticket">
    <!-- Page Header Section -->
    <div class="page-header">
      <v-row
        align="center"
        justify="space-between"
      >
        <v-col cols="auto">
          <div class="d-flex align-center">
            <h2 class="page-title">
              {{ strings.assignTicket.title }}
            </h2>
            <v-chip
              size="x-small"
              class="data-count ml-2"
            >
              Showing {{ tickets.length }} of {{ totalTickets }} Records
            </v-chip>
          </div>
          <v-breadcrumbs
            :items="items"
            class="breadcrumbs"
          >
            <template #title="{ item }">
              {{ item.title }}
            </template>
          </v-breadcrumbs>
        </v-col>
        <v-col
          cols="auto"
          class="page-action"
        >
          <!-- <div class="btn-toolbar">
            <v-btn
              variant="outlined"
            >
              <v-icon>mdi-file-import</v-icon>{{ strings.manageUser.importButton }}
            </v-btn>
          </div> -->
        </v-col>
      </v-row>
    </div>

    <!-- Search and Filter User -->
    <!-- <section class="filter-wrapper">
      <Filter
        v-model:search-value="searchValue"
        :filters="filters"
        :search-placeholder="strings.assignTicket.searchPlaceholder"
        @update:filter-values="updateFilterValues"
      />
    </section> -->
  <section class="page-content-wrapper">
    <section class="card-list-wrapper">
      <!-- Ticket Cards Section -->
      <!-- Loading Indicator -->
      <div
        v-if="loading"
        class="text-center mt-4"
      >
        <v-progress-circular
          indeterminate
          color="blue-lighten-3"
          size="40"
        />
      </div>

      <!-- No Data Message -->
      <div
        v-if="noData && !loading && !errorMessage"
        class="text-center"
        align="center"
      >
        <div class="no-data-available-wrapper">
          <figure>
            <img src="../../assets/images/no-data-available.svg" alt="No Data Available" />
          </figure>
          <label>{{ strings.ticketCategories.noDataAvailable }}</label>
          <small>{{strings.noDataText}}</small>
        </div>
      </div>

      <!-- Ticket List -->
      <div v-if="!loading && !errorMessage && tickets.length > 0">
        <v-card
          v-for="(ticket, index) in tickets"
          :key="index"
          class="app-primary-card"
        >
          <v-row
            align="center"
            justify="space-between"
          >
            <v-col
                  cols="12"
                  sm="12"
                  md="3"
                  lg="3"
                  class="user-data-details"
                >
                  <div class="name-section d-block">
                    <Batch class="batch-ticket-all">
                      {{ strings.assignTicket.ticketId }} -
                      {{ ticket.ticket_id }}
                    </Batch>
                    <div class="user-data-value d-block">
                      {{ ticket.title }}
                    </div>
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  md="3"
                  lg="3"
                  class="user-data-details"
                >
                  <label class="user-data-header">{{
                    strings.assignTicket.projectName
                  }}</label>
                  <div class="user-data-value">
                    {{ ticket.project_name }}
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  md="2"
                  lg="2"
                  class="user-data-details"
                >
                  <label class="user-data-header">{{
                    strings.assignTicket.requesterName
                  }}</label>
                  <div class="user-data-value">
                    {{ ticket.created_by_fullname || "N/A" }}
                  </div>
                </v-col>
                <v-col
                  cols="12"
                  sm="12"
                  md="2"
                  lg="2"
                  class="user-data-details"
                >
                  <label class="user-data-header">{{
                    strings.assignTicket.assignedName
                  }}</label>
                  <div class="user-data-value">
                    {{ ticket.assigned_to_fullname || "N/A" }}
                  </div>
                </v-col>
                <v-col cols="12" sm="12" md="2" lg="2">
                  <div class="btn-toolbar">
                    <v-btn
                      class="btn-outlined-secondary"
                      size="x-small"
                      variant="outlined"
                      icon
                      aria-label="Edit User"
                      @click="editTicketPage(ticket.ticket_id)"
                    >
                      <v-icon>mdi-pencil</v-icon>
                      <v-tooltip
                        activator="parent"
                        location="top"
                      >
                        {{ strings.manageUser.tooltips.edit }}
                      </v-tooltip>
                    </v-btn>
                    <v-btn
                      size="x-small"
                      variant="tonal"
                      icon
                      aria-label="View User"
                      @click="openDialog(ticket.ticket_id)"
                    >
                      <v-icon>mdi-eye</v-icon>
                      <v-tooltip
                        activator="parent"
                        location="top"
                      >
                        {{ strings.manageUser.tooltips.view }}
                      </v-tooltip>
                    </v-btn>
                  </div>
                </v-col>
          </v-row>
        </v-card>
      </div>

      <!-- Loading More Indicator -->
      <div
        v-if="loadingMore"
        class="text-center my-4"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>

      <!-- Warning Message -->
      <p
        v-if="noData && tickets.length > 0"
        class="warning"
      >
        {{ strings.allTicket.noDatas }}
      </p>
    </section>
    <v-dialog
      v-model="dialogVisible"
      max-width="500"
    >
      <div
        v-if="loading"
        class="text-center mt-4"
      >
        <v-progress-circular
          indeterminate
          color="blue-lighten-3"
          size="40"
        />
      </div>
      <!-- <template v-slot:activator="{ props: activatorProps }">
                 
                </template> -->

      <v-card class="assign-ticket-card">
        <v-card-title>
          <h5>{{strings.assignTicket.assignTicketDialog}}</h5>
          <v-btn
            icon
            class="dialog-close-btn"
            @click="dialogVisible = false"
          >
            <v-icon icon="mdi-close" />
          </v-btn>
        </v-card-title>

        <v-card-text>
          <v-row dense>
            <!-- Title Field -->
            <v-col
              cols="12"
              md="12"
            >
              <v-text-field
                v-model="selectedTicket.title"
                :label="strings.assignTicket.assignTitle"
                readonly
                variant="outlined"
              />
            </v-col>

            <!-- Requester Field -->
            <v-col
              cols="12"
              md="12"
            >
              <v-text-field
                v-model="selectedTicket.created_by_fullname"
                :label="strings.assignTicket.assignRequester"
                readonly
                variant="outlined"
              />
            </v-col>

            <!-- Assignee Dropdown -->
            <v-col
              cols="12"
              md="12"
            >
              <v-select
                v-model="selectedTicket.assigned_to"
                label="Assignee*"
                :items="ticketAssigneeOptions"
                item-title="text"
                item-value="value"
                :placeholder="strings.assignTicket.assigneePlaceholder"
                required
                variant="outlined"
              />
              <div>
                <div>
                  <label
                    for="due-date"
                    class="d-block mb-1 mt-3"
                  >{{strings.assignTicket.dueDateLabel}}</label>
                  <input
                    id="due-date"
                    v-model="selectedTicket.due_date"
                    type="datetime-local"
                    :min="minDateTime"
                    class="small-select custom-form-input"
                    required
                  >
                </div>
              </div>
            </v-col>
          </v-row>

          <!-- <small class="text-caption text-medium-emphasis">
            {{ strings.assignTicket.reference }}
          </small> -->
        </v-card-text>

        <v-card-actions>
          <v-btn
            variant="text"
            @click="dialogVisible = false"
          >
            {{ strings.assignTicket.cancelButton }}
          </v-btn>
          <v-btn
            variant="tonal"
            :disabled="isUpdating"
            @click="updateAssignee"
          >
            <template v-if="isUpdating">
              <v-progress-circular
                indeterminate
                size="20"
                class="mr-2"
              />
              {{ strings.common.submittingLoader }}
            </template>
            <template v-else>
              {{ strings.assignTicket.submitButton }}
            </template>
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </section>
  </div>
</template>

<script lang="ts">
import { ref, onMounted, reactive, defineComponent, onUnmounted } from "vue";
import Button from "../../components/Button.vue";
import {
  getLocations,
  getStatus,
  getTickets,
  getUserById,
  apiClient,
} from "../../api/apiClient";
import Batch from "../../components/Batch.vue";
import router from "../../router";
import Filter from "../../components/Filter.vue";
// import { useRouter } from "vue-router";
import { useToast } from "vue-toastification";
import strings from "../../../src/assets/strings.json";
// Define interfaces for your data structures
export interface Ticket {
  ticket_id: number;
  title: string;
  description: string;
  category: number;
  subcategory: number;
  documents: [];
  project: string;
  project_name: string;
  status: string;
  urgency: string;
  location: string;
  created_at: Date;
  updated_at: Date;
  closed_at: number;
  solved_at: number;
  assigned_to_fullname: string;
  created_by_fullname: string;
  assigned_to?: {
    first_name: string;
    email: string;
    [key: string]: any;
  };
  created_by: number; // Add this if not present
  watchers: string;
  createdByUser?: {
    first_name: string;
    email: string;
    [key: string]: any;
  };
  due_date: string;
}
// interface Location {
//   location_name: string;
//   location_id: string;
// }

export default defineComponent({
  name: "AllTickets",
  components: {
    Button,
    Batch,
    Filter,
  },
  setup() {
    // Get users
    // const user = JSON.parse(localStorage.getItem("user") || "{}");
    const tickets = ref<Ticket[]>([]);
    const totalTickets = ref(0);
    const originalTickets = ref<Ticket[]>([]); // To store the original data for resetting filters
    const locations = ref("");
    const locationOptions = ref<{ id: string; location_name: string }[]>([]);
    const dialogVisible = ref(false);

    const page = ref(1);
    const errorMessage = ref<any>("");
    const toast = useToast();
    // pagination
    const noData: any = ref(false);
    const loading = ref(false);
    const loadingMore = ref(false);
    const currentPage = ref(1);
    const totalPages = ref(1);
    const hasMore = ref(true);
    const pageSize = 10;
    const minDateTime = ref("");

    const searchQuery = ref<string>("");

    const statusOptions = ref<any>([]);
    const urgencyOptions = ref<string[]>([
      "low",
      "Medium",
      "high",
      "Very-high",
    ]);
    const requestorOptions = ref<string[]>([
      "John Doe",
      "Jane Smith",
      "Mike Johnson",
    ]);

    const selectedTicket = ref<any>({
      title: "",
      created_by_fullname: "",
      // assignee: {
      //   value: 0,
      //   text: "",
      // },
      ticket_id: 0,
      assigned_to: 0,
      category: "",
      subcategory: "",
      due_date: "",
    });

    const assignedOptions = ref<string[]>([]);
    type AssigneeOption = {
      text: string;
      value: number;
    };

    // Define the type for the ticketAssigneeOptions
    const ticketAssigneeOptions = ref<AssigneeOption[]>([]);

    // Filter Functionality
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    const fetchData = async (isInitialLoad = false) => {
      if (
        loading.value ||
        loadingMore.value ||
        (!hasMore.value && !isInitialLoad)
      )
        return;

      if (isInitialLoad) {
        tickets.value = []; // Reset ticket list
        noData.value = false;
        currentPage.value = 1; // 🔁 Reset current page
        hasMore.value = true;
      }

      if (!isInitialLoad) {
        loadingMore.value = true;
      }

      loading.value = isInitialLoad;

      try {
        const response = await apiClient.get("assignee-update/", {
          params: {
            page: currentPage.value, // 👈 Use this to fetch paginated data
          },
        });

        let ticketsData = response.data.results || [];
        ticketsData = ticketsData.filter(
          (ticket: any) => ticket.assigned_to === null
        );

        if (ticketsData.length > 0) {
          if (isInitialLoad) {
            tickets.value = ticketsData;
          } else {
            tickets.value = [...tickets.value, ...ticketsData];
          }

          totalPages.value = response.data.total_pages || 1;
          currentPage.value++; // ✅ Move to next page
          hasMore.value = currentPage.value <= totalPages.value;
          noData.value = false;
          totalTickets.value = response.data.count || 0;
        } else {
          hasMore.value = false;
          noData.value = tickets.value.length === 0;
        }
      } catch (error: any) {
        noData.value = true;
        console.error("Error fetching tickets:", error);

        if (error.response?.status === 404) {
          // Redirect to custom 404 page
          router.push("/404");
        } else if (error.response?.status === 401) {
          // Optional: handle session expiration
          router.push("/session-expired");
        } else {
          toast.error("Error loading tickets.");
        }
        if (error.response?.status === 400) {
          hasMore.value = false;
        } else {
          errorMessage.value = "Failed to fetch tickets.";
        }
      } finally {
        loading.value = false;
        loadingMore.value = false;
      }
    };

    const handleScroll = () => {
      if (loading.value || loadingMore.value || !hasMore.value || noData.value)
        return;

      const scrollPosition = window.innerHeight + window.scrollY;
      const documentHeight = document.documentElement.scrollHeight;

      if (scrollPosition >= documentHeight - 150) {
        fetchData(); // Fetch next page
      }
    };

    onMounted(() => {
      fetchData(true); // Initial load
      window.addEventListener("scroll", handleScroll);
    });

    onUnmounted(() => {
      window.removeEventListener("scroll", handleScroll);
    });

    const openDialog = async (ticketId: number) => {
      try {
        // Fetch ticket details by ID
        const response = await apiClient.get(`/tickets/${ticketId}`);
        const ticketData = response.data;

        // Fetch all users
        const allUsersResponse = await apiClient.get("/users/");
        const allUsers = allUsersResponse.data.data;

        let assigneeOptions: AssigneeOption[] = allUsers.map(
          (user: {
            id: number;
            first_name: string;
            last_name: string;
            role_name: string;
          }) => ({
            text: `${user.first_name} ${user.last_name}`, // Full name as text
            value: user.id, // User ID as value
            role: user.role_name, // Store role for filtering
          })
        );

        assigneeOptions = assigneeOptions.filter((user: any) =>
          ["Super Admin","Admin", "Technician"].includes(user.role)
        );

        // Update the selected ticket with fetched data
        selectedTicket.value = {
          ...ticketData,
          category: ticketData.category || null,
          subcategory: ticketData.subcategory || null,
          assigned_to:
            assigneeOptions.find(
              (option: AssigneeOption) =>
                option.value === ticketData.assigned_to
            ) || null, // Match by value (user ID)
        };

        // Store the assignee options in the ref
        ticketAssigneeOptions.value = assigneeOptions;
        // Show the modal dialog
        dialogVisible.value = true;
      } catch (error: any) {
        console.error("Error fetching ticket details or users:", error);
      }
    };

    const editTicketPage = (ticket_id: number) => {
      router
        .push({
          name: "/TicketManagement/editTicket",
          params: { ticket_id }, // Convert to string as route params are strings
          query: { source: "assign-ticket" },
        })
        .catch((err) => {
          if (err.name !== "NavigationDuplicated") {
            console.error("Navigation Error:", err);
          }
        });
    };

    const isUpdating = ref(false); // State for loader

    const updateAssignee = async () => {
      try {
        if (selectedTicket.value.due_date) {
          const selectedDueDate = new Date(selectedTicket.value.due_date);
          const now = new Date();
          if (selectedDueDate.getTime() <= now.getTime()) {
            toast.error("Please select a future date and time.");
            return; // Prevent submission if due date is in the past
          }
        }

        if (!selectedTicket.value.assigned_to) {
          console.error("Selected assignee is missing");
          toast.error("Please select a valid assignee.");
          return;
        }

        isUpdating.value = true; // Start loader
        const payload = {
          assigned_to: selectedTicket.value.assigned_to,
          due_date: selectedTicket.value.due_date,
          status: 1,
        };
        const response = await apiClient.patch(
          `/assignee-ticket/${selectedTicket.value.ticket_id}/update/`,
          payload
        );

        // ✅ Remove the updated ticket from the list immediately
        tickets.value = tickets.value.filter(
          (ticket) => ticket.ticket_id !== selectedTicket.value.ticket_id
        );

        dialogVisible.value = false; // Close the dialog after saving
        await fetchData(); // Refresh data
        toast.success("Ticket updated successfully!");
      } catch (error: any) {
        console.error("Error updating ticket:", error);
        const errorMessage = error.response?.data?.detail;

        if (errorMessage) {
          toast.error(errorMessage);
        } else {
          toast.error("Something went wrong. Please try again later.");
        }
      } finally {
        isUpdating.value = false; // Stop loader after API response
      }
    };

    const filters = ref([
      {
        id: "status",
        placeholder: "-- Select Status --",
        options: [],
      },
      {
        id: "location",
        placeholder: "-- Select Location --",
        options: [],
      },
      {
        id: "created_by",
        placeholder: "-- Requster --",
        options: [],
      },
      {
        id: "assigned_to",
        placeholder: "-- Assigned --",
        options: [],
      },
    ]);

    const searchValue = ref<string>("");

    const filterParams = ref<Record<string, string | number | null>>({});

    return {
      tickets,
      locations,
      locationOptions,
      // filters,
      searchQuery,
      statusOptions,
      urgencyOptions,
      requestorOptions,
      assignedOptions,
      ticketAssigneeOptions,
      editTicketPage,

      // resetFilters,
      searchValue,
      filters,
      // user,
      dialogVisible,
      selectedTicket,
      openDialog,
      updateAssignee,
      isUpdating,
      loading,
      totalTickets,
      strings,
      noData,
      errorMessage,
      loadingMore,
      minDateTime,
    };
  },
  data: () => ({
    items: [
      { title: "Home", disabled: false, href: "/ticket-management" },
      {
        title: "Ticket Management",
        disabled: false,
        href: "/all-tickets",
      },
      {
        title: "Assign Tickets",
        disabled: false,
        href: "",
      },
    ],
    dialog: false,
  }),
});
</script>

<style>
.custom-breadcrumbs {
  color: #8c8b90;
  font-size: 12px;
}
.v-breadcrumbs {
  padding: 0% !important;
}
.v-breadcrumbs--density-default {
  padding: 0% !important;
}
.breadcrums-heading {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: space-between !important;
  /* width: 77%; */
}
</style>

<style scoped>
h1 {
  font-size: 27px;
  font-weight: 700;
  margin: 0;
}
.card {
  background: white;
  border: 2px solid #d8d8d8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  /* width: 77%; */
  padding: 12px 24px;
  cursor: pointer;
}
.card:hover {
  background-color: #f9f9f9;
  transition: background-color 0.3s, color 0.3s;
}
.hover-content {
  display: none;
}

.assign-ticket-card {
  border-radius: 8px;
}

.dialog-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  background-color: #1565c0; /* Matches the blue header color in the image */
  padding: 12px;
}

/* .cancel-btn {
  background-color: #e0e0e0;
  color: #757575;
}

.submit-btn {
  background-color: #4caf50;
  color: white;
} */

/* .card:hover,
.hover-content {
  display: block;
  position: absolute;
  z-index: 1000;
  width: 100%;
  height: 150px;
} */
.card-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.custom-card-item {
  padding: 10px;
  display: flex;
  /* justify-content: space-between !important; */
  align-items: center;
  width: 100%;
  gap: 40px;
}
.custom-card {
  /* padding: 6px; */
  display: flex;
  gap: 4px;
  flex-direction: column;
}

.custom-card span {
  font-size: 14px;
  font-weight: 500;
  color: #8c8c8c;
}
.custom-card p {
  font-size: 14px;
  font-weight: 500;
  color: #2e2e2e;
}

.filter-fields {
  display: flex;
  flex-wrap: wrap;
  /* align-items: center; */
  justify-content: space-evenly;
}

.action-btn {
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  gap: 12px;
}
.view-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #026bb1;
  border-radius: 50%;
  color: #026bb1;
}
.edit-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #2dcc70;
  border-radius: 50%;
  color: #2dcc70;
}
.custom-btn {
  background: none;
  border: 1px solid #2dcc70;
  border-radius: 5px;
  color: #2dcc70;
  box-shadow: none !important;
  margin: 0;
  text-transform: none;
}
.high-btn {
  background: none;
  border: 1px solid #ff7780;
  border-radius: 50px;
  color: #ff7780;
  box-shadow: none !important;
  margin: 0;
  padding: 0%;
  text-transform: none;
  max-width: 57px;
}

.filters-wrapp {
  margin-top: 20px;
  /* width: 77%; */
  display: flex;
  flex-wrap: wrap;
  background-color: #f9f9f9;
  padding: 12px;
  align-items: center;
  gap: 12px;
}

.search-icon {
  background: #2dcc70;
  color: #fff;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
/* .search-icon:hover {
  color: #2dcc70;
  background: #fff;
} */
.cached-icon {
  background: #e6e6e6;
  color: #b5b5b5;
  width: 38px;
  height: 38px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

/* Custom styles for inputs */

input,
.v-select__selections,
.v-text-field input {
  background: transparent;
  outline: none;
  box-shadow: none;
  font-size: 14px;
}

.v-select__control,
.v-text-field__control {
  border-radius: 12px; /* Same border radius for select */
  border: 1px solid #d8d8d8; /* Same border color */
}

.v-select__control:focus,
.v-text-field__control:focus {
  border-color: #2dcc70; /* Same focus color */
}
.v-icon {
  font-size: 14px !important;
}
.v-list-item__append > .v-icon {
  font-size: 14px !important;
}
</style>
