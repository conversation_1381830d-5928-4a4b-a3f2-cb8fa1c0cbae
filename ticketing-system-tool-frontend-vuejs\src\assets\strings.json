{"common": {"loading": "Loading...", "updating": "Updating...", "noDataAvailable": "No data available", "optional": "Optional", "required": "Required", "characterLimit": "{current} / {max} characters", "charactersOver": "({over} over limit)", "charactersRemaining": "({remaining} more needed)", "addCategoryButton": "Add Category", "backButton": "Back", "submitButton": "Submit", "cancelButton": "Cancel", "clearButton": "Clear", "closeButton": "Close", "reopenButton": "Reopen", "exportButton": "Export", "importButton": "Import", "addButton": "Add", "editButton": "Edit", "viewButton": "View", "deleteButton": "Delete", "uploadButton": "Upload", "downloadButton": "Download", "saveButton": "Save", "confirmText": "Yes, Confirm", "cancelText": "No, Cancel", "uploadDocumentButton": "Upload Documents", "sendMessageButton": "Send Message", "search": "Search...", "selectPlaceholder": "-- Select {field} --", "selectRolePlaceholder": "-- Select Role --", "selectLocationPlaceholder": "-- Select Location --", "selectPriorityPlaceholder": "-- Select Priority --", "referenceRequired": "**indicates required field", "noDataText": "No Data Available", "noDataAvailableText": "There is no data to show you right now", "filesizeText": "Please make sure the file size does not exceed 2 MB.", "ticketApproval": "Ticket <PERSON>al", "submittingLoader": "Submitting..."}, "dialogs": {"confirmation": {"title": "Confirmation", "message": "Are you sure you want to proceed?", "confirmButton": "Yes, Proceed", "cancelButton": "No, Cancel"}, "delete": {"title": "Delete {item}", "message": "Are you sure you want to delete {item}?", "confirmButton": "Yes, Delete", "cancelButton": "No, Cancel"}, "export": {"title": "Export Options", "excelOption": "Export to Excel", "pdfOption": "Export to PDF"}, "import": {"title": "Import {item}", "sampleText": "If you don't have a file you can use the sample below", "downloadSample": "Download Sample"}, "removeApproval": {"title": "Remove Approval Request?", "message": "This action requires confirmation. Are you sure you want to remove this approval request?"}}, "pendingApprovals": {"title": "Pending Approval", "exportButton": "Export", "ticketSearch": "Search tickets...", "noData": "No Data Available", "ticketTitle": "Title", "ticketId": "Ticket Id", "projectTitle": "Project Name", "requesterName": "Requester", "assignedName": "Assigned", "ticketApproval": "Ticket <PERSON>al", "approvalLabel": "Approval status", "messageLabel": "Approval Message", "cancelmessageLabel": "Cancel Message", "UploadDocumentsLabel": "Upload Documents(optional)", "cancelButton": "Cancel", "SubmitButton": "Submit"}, "textEditor": {"placeholder": "Type your message here..."}, "firebase": {"notificationTitle": "New Notification", "notificationBody": "You have a new notification"}, "login": {"title": "<PERSON><PERSON>", "emailPlaceholder": "Email Address", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot Password?", "loginButton": "Log In", "displaymessage": "Glad to see you again. Login to your account", "welcomemessasge": "Welcome Back!"}, "logout": {"title": "Logout", "message": "Are you sure you want to logout?", "confirmButton": "Yes, <PERSON><PERSON><PERSON>", "cancelButton": "No, Cancel", "notification": "Notification", "clearAll": "Clear All", "noNotification": "No New notifications!", "welcome": "Welcome !", "profileIcon": {"myAccount": "My Account", "changePassword": "Change Password", "logout": "Logout"}}, "forgotPassword": {"title": "Forgot Password", "emailPlaceholder": "Enter your email address", "submitButton": "Send Reset Link", "emailError": "Email is required.", "successMessage": "A password reset link has been sent to your email address.", "errorMessage": "There was an issue with the reset request. Please try again.", "genericErrorMessage": "Something went wrong. Please try again later.", "displaymessage": "Enter your email address and we will send you instructions to reset your password"}, "resetPassword": {"title": "Reset Password", "passwordPlaceholder": "Enter new password", "confirmPasswordPlaceholder": "Confirm new password", "resetButton": "Reset Password", "passwordError": "Password is required.", "confirmPasswordError": "Confirm password is required.", "passwordMismatchError": "Passwords do not match.", "passwordLengthError": "Password must be at least 6 characters.", "successMessage": "Your password has been reset successfully.", "errorMessage": "Failed to reset password. Please try again.", "displaymessage": "Enter a new password below to change your password", "buttonMessage": "Update Password", "backToForgetpassowrd": "Back to Forgot Password"}, "allTicket": {"title": "All Ticket", "exportButton": "Export", "ticketSearch": "Search tickets...", "noData": "No Data Available", "ticketTitle": "Title", "ticketId": "Ticket Id", "projectTitle": "Project Name", "requesterName": "Requester", "assignedName": "Assigned", "statusName": "Status", "noDatas": "No More Data Available", "createdAt": "Created At", "updatedAt": "Updated At", "ticketIds": "Ticket ID - #"}, "createTicket": {"title": "Create Ticket", "ticketLabel": "Ticket Title", "ticketPlaceholder": "Enter the Ticket Title", "emailLabel": "Watchers (Email IDs)", "emailPlaceholder": "Add Email Ids", "projects": "Projects", "projectPlaceholder": "Enter the projects", "priority": "Priority", "priorityPlaceholder": "Enter the priority", "uploadDocuments": "Upload Document file (optional)", "justification": "Justification", "justificationPlaceholder": "Enter the justification", "submitButton": "Submit", "cancelButton": "Cancel", "uploadDocumentsText": "Please make sure the file size does not exceed 2 MB.", "tipsTitle": "Ticket Creation Tips!", "tipsDescription": "When creating a new ticket, Clearly mention the necessary supporting details in the ticket description.", "trackProgressTitle": "Track Progress", "trackProgressText": "You can view ticket status anytime under \"Ticket Tracking\".", "addCommentsTitle": "Add Comments", "addCommentsText": "You can add updates or respond to IT team queries within the ticket.", "resolveCloseTitle": "Resolve / Close", "resolveCloseText": "Once the issue is fixed, confirm and mark the ticket as Close (or Ticket will close it after 72 Hours).", "needHelpTitle": "Need Help?", "callHelpdesk": "Call The IT Helpdesk", "helpDeskEmail": "<EMAIL>", "approvalDialogTitle": "Need Ticket Approval?", "approvalDialogText": "This action requires confirmation. Do you want to request your manager's approval for this ticket?", "proceedWithoutApproval": "Proceed, Without Approval", "needApproval": "Yes, Need Approval", "descriptionLabel": "Description", "descriptionPlaceholder": "Enter detailed description of the issue", "characterCount": "{current} / {required} characters", "charactersRemaining": "({remaining} more needed)"}, "editTicket": {"title": "Edit Tickets", "backButton": "Back", "currentAssigneeName": "Current Assignee", "currentpriorityName": "Current Priority", "currentStatusName": "Current Status", "ticketInformation": "Ticket Information", "projectName": "Project Name", "location": "Location", "createDate": "Created Date", "updateDate": " Last Updated Date", "uploadDocuments": "Documents", "ticketComments": "Ticket comments", "conversartions": "Conversations", "ticketTitle": "Title", "ticketId": "Ticket ID", "category": "Ticket Category", "subcategory": "Ticket Sub Category", "status": "Status", "dueDateExtensionLabel": "Due Date Extension", "assignee": "Assignee", "priority": "Priority", "submitButton": "Submit", "cancelButton": "Cancel", "commentText": "Comments", "watchers": "Watchers", "editTicketTabTitles": {"ticketInfo": "Ticket Info", "ticketDescription": "Ticket Description", "ticketTracking": "Ticket Tracking"}, "ticketDescriptionLabel": {"documents": "Documents", "ticketComments": "Ticket Comments", "createdBy": "Created By", "createdDate": "Created Date", "updatedDate": "Last Updated Date", "approvedBy": "Approved By", "description": "Description", "justification": "Justification", "approvalMessage": "Approval Message", "cancelReason": "Cancel Reason"}, "ticketTrackingLabel": {"status": "Status", "updatedBy": "Updated by:", "createdBy": "Created by", "description": "Description", "date": "Date"}}, "feedback": {"title": "Give us your feedback", "description": "What was your experience while working with our IT Team?", "submitButton": "Submit", "cancelButton": "Cancel", "reasonLabel": "Reason", "reasonPlaceholder": "Enter your reason"}, "assignedIt": {"title": "Edit Ticket", "backButton": "Back", "currentAssigneeName": "Current Assignee", "currentpriorityName": "Current Priority", "currentStatusName": "Current Status", "projectName": "Project Name", "locationName": "Location", "createdDate": "Created Date", "updatedDate": "Last Updated Date", "documentsName": "Documents", "ticketComments": "Ticket Comments", "ticketTitle": "Title", "ticketId": "Ticket ID", "assigneeName": "Assignee", "submitButton": "Submit", "cancelButton": "Cancel"}, "assignTicket": {"title": "Unassign Ticket", "exportButton": "Export", "searchPlaceholder": "Search tickets...", "ticketTitle": "Title", "ticketId": "Ticket Id", "projectName": "Project Name", "requesterName": "Requester", "assignedName": "Assigned", "submitButton": "Submit", "cancelButton": "Cancel", "assignTitle": "Title", "assignRequester": "Requester", "assigneePlaceholder": "-- Select Assignee --", "reference": "**indicates required field", "assignTicketDialog": "Assign <PERSON>", "dueDateLabel": "Due Date", "updatingButton": "Updating..."}, "assignView": {"title": "View Ticket", "editbutton": "Edit", "backButton": "Back", "assignTicketTitle": "Title", "assignTicketId": "Ticket Id", "assignRequester": "Requester", "assigned": "Assigned", "projectName": "Project Name", "locationName": "Location", "createdDate": "Created Date", "updateDate": "Last Update Date", "category": "Category", "subcategory": "Sub Category", "documents": "Documents", "ticketComments": "Ticket Comments", "addcomments": "Add comments"}, "pendingApproval": {"title": "Pending Approval", "exportButton": "Export", "ticketSearch": "Search tickets...", "noData": "No Data Available", "ticketTitle": "Title", "ticketId": "Ticket Id", "projectTitle": "Project Name", "requesterName": "Requester", "assignedName": "Assigned", "ticketApproval": "Ticket <PERSON>al", "approvalLabel": "Approval status", "messageLabel": "Approval Message", "cancelmessageLabel": "Cancel Message", "UploadDocumentsLabel": "Upload Documents(optional)", "cancelButton": "Cancel", "SubmitButton": "Submit", "cancelApprovalTitle": "<PERSON>cel <PERSON>", "cancelApprovalText": "Are you sure you want to Cancel this approval?", "rejectApprovalButton": "Yes,Reject", "cancelApprovalButton": "No, Cancel", "noDataText": "No Data Available"}, "pendingEditApproval": {"title": "Edit Ticket", "backButton": "Back", "projectName": "Project Name", "locationName": "Location", "createdDate": "Created Date", "updatedDate": "Last Updated Date", "documentsName": "Documents", "ticketComments": "Ticket Comments", "conversartions": "Conversations", "ticketApproval": "Ticket <PERSON>al", "approvalStatus": "ApprovalStatus", "approvalMessage": "Message", "uploadDocuments": "Upload Documents", "cancelButton": "Cancel", "submitButton": "Submit", "pendingEditApprovalTabTitles": {"ticketInfo": "Ticket Info", "ticketDescription": "Ticket Description", "ticketTracking": "Ticket Tracking"}, "ticketDescriptionLabel": {"documents": "Documents", "ticketComments": "Ticket Comments", "createdBy": "Created By", "createdDate": "Created Date", "updatedDate": "Last Updated Date", "approvedBy": "Approved By", "description": "Description", "justification": "Justification", "approvalMessage": "Approval Message", "cancelReason": "Cancel Reason"}, "ticketTrackingLabel": {"status": "Status", "updatedBy": "Updated by:", "createdBy": "Created by", "description": "Description", "date": "Date"}}, "pendingViewTicket": {"title": "View Ticket", "editButton": "Edit", "backButton": "Back", "ticketTitle": "Title", "ticketId": "Ticket Id", "Requester": "Requester", "assigned": "Assigned", "projectName": "Project Name", "locationName": "Location", "createdDate": "Created Date", "updatedDate": "Last Update Date", "category": "Category", "subcategory": "Sub Category", "documents": "Documents", "ticketComments": "Ticket Comments", "addComments": "Add comments"}, "viewTicket": {"title": "View Ticket", "editButton": "Edit", "backButton": "Back", "viewTicketTitle": "Title", "viewTicketId": "Ticket Id", "currentPriority": "Current Priority", "viewRequester": "Requester", "assigned": "Assigned", "projectName": "Project Name", "locationName": "Location", "createdDate": "Created Date", "updateDate": "Last Update Date", "category": "Category", "subcategory": "Sub Category", "documents": "Documents", "ticketComments": "Ticket Comments", "addcomments": "Add comments"}, "ticketsReports": {"title": "Ticket Report", "yearlyReports": "Yearly Report", "categoryGraph": "Category Report", "locationGraph": " Location Report"}, "technicianPerformence": {"title": "Tickets Report", "categoryGraph": "Category Report"}, "userFeedback": {"title": "Tickets Report", "technicainFeedback": "Technician <PERSON>", "categoryFeedback": "Category Feedback"}, "sidebarMenu": {"dashboard": "Dashboard", "ticketManagementTitle": "Ticket Management", "ticketManagementMenus": {"allTickets": "All Tickets", "createTicket": "Create Ticket", "pendingTicket": "Pending Ticket", "assignTicket": "Assign <PERSON>"}, "userManagementTitle": "User Management", "userManagementMenus": {"allUsers": "Manage Users", "createUser": "Add New User", "roleAssignment": "Role Assignment", "projectMapping": "Project Mapping"}, "reportAndAnalyticsTitle": "Report & Analytics", "reportAndAnalyticsMenus": {"ticketsAndReports": "Tickets & Reports", "technicianReport": "Technician Report", "userFeedback": "User <PERSON>"}, "itSupportConfigTitle": "IT Support Config", "itSupportConfigMenus": {"createCategories": "Create Category", "emailTemplate": "<PERSON>ail Te<PERSON>late"}}, "dashboard": {"title": "Dashboard", "reports": {"yearlyReport": "Ticket Report", "categoryReport": "Category Report", "employeeReport": "Employee Report (Top 5)"}, "datepickerPlaceholder": "Select date range", "exportButton": "Export", "yearlyReportLabel": {"lableXAxis": "Periods", "lableYAxis": "Number of Tickets"}, "exportDialogTitle": "Export Options", "exportOptions": {"excel": "Export to Excel", "pdf": "Export to PDF"}, "buttons": {"close": "Close", "submit": "Submit"}}, "addUser": {"title": "Add New User", "backButton": "Back", "clearButton": "Clear", "submitButton": "Submit", "profilePicture": "Profile Picture", "optional": "Optional", "uploadButton": "Upload", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phoneNumber": "Phone Number", "employeeId": "Employee ID", "role": "Role", "location": "Location", "validation": {"required": "{field} is required.", "general": "Please fill out all required fields."}, "successMessage": "User added successfully!", "errorMessage": "Failed to add user. Please try again."}, "manageUser": {"title": "All User", "addUserButton": "Add User", "importButton": "Import", "searchAndFilter": {"search": "Search users by Employee Id, Employee Name", "statusFilter": "--Select Status--", "roleFilter": "--Select Role--", "locationFilter": "--Select Location--", "priorityFilter": "--Select Priority--", "ticketListSearch": "Search TicketID/Title"}, "noDataText": "No Data Available", "tooltips": {"edit": "Edit", "delete": "Remove", "view": "View", "block": "Block", "unBlock": "UnBlock "}, "cancelText": "No, Cancel", "confirmDeleteText": "Yes, Delete", "deleteDialogTitle": "Delete User", "deleteDialogText": "Are you sure you want to Delete user", "importdialogHeader": "Import User", "importSmapletext": "If you don't have a file you can use the sample below", "buttons": {"upload": "Upload", "cancel": "Cancel", "downloadSample": "Download Sample"}}, "editUser": {"title": "Edit User", "buttons": {"view": "View", "back": "Back", "clear": "Clear", "submit": "Submit", "uploadProfile": "Upload", "removeProfile": "Remove"}, "profilePicture": "Profile Picture", "optional": "(Optional)", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phoneNumber": "Phone Number", "employeeId": "Employee ID", "role": "Role", "location": "Location", "validation": {"required": "{field} is required.", "general": "Please fill out all required fields."}, "updateSuccessMessage": "User updated successfully!"}, "viewUser": {"title": "View User", "buttons": {"edit": "Edit", "back": "Back"}, "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phoneNumber": "Phone Number", "employeeId": "Employee ID", "role": "Role", "location": "Location"}, "roleAssignment": {"title": "Role Assignment", "buttons": {"addRole": "Add Role", "add": "Add", "update": "Update"}, "noDataText": "No Role Available.", "moduleAccessText": "Module Access", "validateMessage": "Please select atleast one submodule."}, "projectMapping": {"title": "Projects", "buttons": {"addProject": "Add Project", "editProject": "Edit", "viewProject": "View", "saveProject": "Save", "cancelProject": "Cancel", "closeProject": "Close", "deleteProject": "Delete"}, "noDataText": "No Project Avaibale.", "projectName": "Project Name", "projectManager": "Project Manager", "projectStatus": "Status", "addProjectDialogHeader": "Add Project", "projectManagerPlaceholder": "Select a Project Manager", "projectMemberPlaceholder": "Select Members", "editProjectTitle": "Edit Project", "viewProjectTitle": "View Project", "deleteProjectTitle": "Delete Project", "deleteDialogConfirmation": "Are you sure you want to delete project"}, "ticketCategories": {"title": "Ticket Categories", "noDataAvailable": "No Data Available", "cardHeaders": {"category": "Category", "subcategory": "Sub Category", "createdBy": "Created By", "updatedBy": "Updated By", "is_active": "Status"}, "deleteDialog": {"title": "Delete Category", "buttons": {"confirm": "Yes, Delete", "cancel": "No, Cancel"}}, "activateDeactivateDialog": {"description": "Are you sure you want to {action} category?"}, "addDialogTitle": "Add Category", "editDialogTitle": "Edit Category", "viewDialogTitle": "View Category"}, "noDataText": "There is no data to show you right now.", "notFound404": {"title": "Oops! Page Not Found", "message": "We are very sorry for the inconvenience. It looks like you're trying to access a page that either has been deleted or never existed.", "buttonText": "Go to Dashboard"}, "sessionExpired": {"title": "Oops! Session Expired", "message": "Looks like your session has timed out. For your security, please login again", "buttonText": "Login Again"}}