import { defineStore } from 'pinia';

export const useTicketFilterStore = defineStore('ticketFilter', {
  state: () => ({
    searchValue: '',
    status: '',
    priority: '',
    locations: '',
    isFiltering: false
  }),
  
  actions: {
    updateFilters(filters:any) {
      this.searchValue = filters.searchValue;
      this.status = filters.status;
      this.priority = filters.priority;
      this.locations = filters.locations;
      this.isFiltering = filters.isFiltering;
      
      // Save to localStorage for persistence across page refreshes
      localStorage.setItem('ticketFilters', JSON.stringify({
        searchValue: this.searchValue,
        status: this.status,
        priority: this.priority,
        locations: this.locations,
        isFiltering: this.isFiltering
      }));
    },
    
    clearFilters() {
      this.searchValue = '';
      this.status = '';
      this.priority = '';
      this.locations = '';
      this.isFiltering = false;
      
      // Clear from localStorage
      localStorage.removeItem('ticketFilters');
    },
    
    // Initialize from localStorage if available
    initFromStorage() {
      const savedFilters = localStorage.getItem('ticketFilters');
      if (savedFilters) {
        const filters = JSON.parse(savedFilters);
        this.searchValue = filters.searchValue;
        this.status = filters.status;
        this.priority = filters.priority;
        this.locations = filters.locations;
        this.isFiltering = filters.isFiltering;
      }
    }
  }
});