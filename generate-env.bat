@echo off
setlocal

REM Script to generate environment files for different environments
REM Usage: generate-env.bat [environment]
REM Example: generate-env.bat staging

REM Default to development if no environment is specified
set ENV=%1
if "%ENV%"=="" set ENV=development

echo Generating environment file for %ENV% environment...

if "%ENV%"=="development" (
    echo VITE_API_BASE_URL=http://127.0.0.1:8000/api/ > .env.development
    echo VITE_WS_URL=ws://localhost:8000/ws/ticket/ >> .env.development
    echo Generated .env.development file
) else if "%ENV%"=="staging" (
    echo VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/ > .env.staging
    echo VITE_WS_URL=ws://localhost:8000/ws/ticket/ >> .env.staging
    echo Generated .env.staging file
) else if "%ENV%"=="production" (
    echo VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/ > .env.production
    echo VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/ >> .env.production
    echo Generated .env.production file
) else (
    echo Unknown environment: %ENV%
    echo Available environments: development, staging, production
    exit /b 1
)

echo Environment file generation complete!
