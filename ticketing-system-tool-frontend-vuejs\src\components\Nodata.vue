<template>
  <div class="text-center no-data-available" align="center">
   
    <div class="no-data-available-wrapper">
      <figure>
        <img
          src="../assets/images/no-data-available.svg"
          alt="No Data Available"
        />
      </figure>
      <label>{{strings.common.noDataText}}</label>
      <small>{{strings.common.noDataAvailableText}}</small>
    </div>
  </div>
</template>

<script setup lang="ts">
import strings from "../assets/strings.json";
</script>


