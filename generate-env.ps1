# Script to generate environment files for different environments
# Usage: .\generate-env.ps1 [environment]
# Example: .\generate-env.ps1 staging

param (
    [string]$Environment = "development"
)

Write-Host "Generating environment file for $Environment environment..."

switch ($Environment) {
    "development" {
        "VITE_API_BASE_URL=http://127.0.0.1:8000/api/" | Out-File -FilePath .env.development -Encoding utf8
        "VITE_WS_URL=ws://localhost:8000/ws/ticket/" | Out-File -FilePath .env.development -Append -Encoding utf8
        Write-Host "Generated .env.development file"
    }
    "staging" {
        "VITE_API_BASE_URL=http://internal-project.nexware-global.com:9019/api/" | Out-File -FilePath .env.staging -Encoding utf8
        "VITE_WS_URL=ws://localhost:8000/ws/ticket/" | Out-File -FilePath .env.staging -Append -Encoding utf8
        Write-Host "Generated .env.staging file"
    }
    "production" {
        "VITE_API_BASE_URL=https://ticket.nexware-global.com:9049/api/" | Out-File -FilePath .env.production -Encoding utf8
        "VITE_WS_URL=wss://internal-project.nexware-global.com:8001/ws/ticket/" | Out-File -FilePath .env.production -Append -Encoding utf8
        Write-Host "Generated .env.production file"
    }
    default {
        Write-Host "Unknown environment: $Environment"
        Write-Host "Available environments: development, staging, production"
        exit 1
    }
}

Write-Host "Environment file generation complete!"
