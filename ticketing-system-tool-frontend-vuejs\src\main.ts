import { createApp } from "vue";
import App from "./App.vue";
import { createVuetify } from "vuetify";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";
import router from "./router";

//Styles
import "@mdi/font/css/materialdesignicons.css";
import "bootstrap/dist/css/bootstrap.min.css";
import "bootstrap";
import "vue-toastification/dist/index.css";
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';

//Plugins & Components
import Toast from "vue-toastification";
import AppButton from "./components/Button.vue";
import { library } from "@fortawesome/fontawesome-svg-core";
import { fas } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

// Plugins
import { registerPlugins } from "@/plugins";

library.add(fas);

const app = createApp(App);
const vuetify = createVuetify({});
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

app.component("font-awesome-icon", FontAwesomeIcon);
app.component("AppButton", AppButton);

app.use(pinia);
app.use(router);
app.use(vuetify);
app.use(Toast, {
  position: "top-right",
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: false,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: true,
  closeButton: "AppButton",
  icon: true,
});

// if ("serviceWorker" in navigator) {
//   navigator.serviceWorker
//     .register("/firebase-messaging-sw.js")
//     .then((registration) => {
//       console.log("Service Worker registered:", registration);
//     })
//     .catch((error) => {
//       console.error("Service Worker registration failed:", error);
//     });
// }


registerPlugins(app);
app.mount("#app");