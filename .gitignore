# Node.js dependencies
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
package-lock.json
yarn.lock

# Build output
dist/
build/
out/
.output/

# Environment variables
.env
.env.*
.env.local
.env.*.local
# Keep example env file and Jenkins files
!.env.example
!Jenkinsfile
!jenkins*.md
!generate-env.sh

# IDE and editor files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Testing
coverage/
.nyc_output/

# Temporary files
.tmp/
.temp/
.cache/
