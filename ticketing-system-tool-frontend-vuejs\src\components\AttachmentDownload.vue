<template>
  <div class="attachment-download">
    <!-- Single attachment display -->
    <div 
      v-if="!multiple && attachment" 
      class="attachment-item"
      :class="{ 'downloading': downloading }"
    >
      <v-btn
        variant="text"
        class="attachment-btn"
        :disabled="downloading"
        @click="handleDownload(attachment)"
      >
        <v-icon 
          :icon="getFileIcon(attachment.original_filename || attachment.display_filename)" 
          class="mr-2"
        />
        <span class="attachment-name">
          {{ attachment.display_filename || attachment.original_filename }}
        </span>
        <v-icon 
          v-if="downloading" 
          icon="mdi-loading" 
          class="ml-2 rotating"
        />
        <v-icon 
          v-else 
          icon="mdi-download" 
          class="ml-2"
        />
      </v-btn>
    </div>

    <!-- Multiple attachments display -->
    <div v-else-if="multiple && attachments && attachments.length > 0" class="attachments-list">
      <div class="attachments-header">
        <h6>{{ title || 'Attachments' }}</h6>
        <v-btn
          v-if="attachments.length > 1"
          variant="outlined"
          size="small"
          :disabled="downloading"
          @click="downloadAll"
        >
          <v-icon icon="mdi-download-multiple" class="mr-1" />
          Download All
        </v-btn>
      </div>
      
      <div class="attachments-grid">
        <div
          v-for="(attachment, index) in attachments"
          :key="index"
          class="attachment-item"
          :class="{ 'downloading': downloadingItems.has(attachment.id) }"
        >
          <v-btn
            variant="text"
            class="attachment-btn"
            :disabled="downloadingItems.has(attachment.id)"
            @click="handleDownload(attachment)"
          >
            <v-icon 
              :icon="getFileIcon(attachment.original_filename || attachment.display_filename)" 
              class="mr-2"
            />
            <span class="attachment-name">
              {{ attachment.display_filename || attachment.original_filename }}
            </span>
            <v-icon 
              v-if="downloadingItems.has(attachment.id)" 
              icon="mdi-loading" 
              class="ml-2 rotating"
            />
            <v-icon 
              v-else 
              icon="mdi-download" 
              class="ml-2"
            />
          </v-btn>
        </div>
      </div>
    </div>

    <!-- No attachments message -->
    <div v-else class="no-attachments">
      <v-icon icon="mdi-attachment" class="mr-2" />
      <span>No attachments available</span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, PropType } from 'vue';
import { fileDownloadService } from '@/services/fileDownloadService';

interface Attachment {
  id: number;
  attachement: string;
  original_filename: string;
  display_filename: string;
  file_url?: string;
}

export default defineComponent({
  name: 'AttachmentDownload',
  props: {
    attachment: {
      type: Object as PropType<Attachment>,
      default: null
    },
    attachments: {
      type: Array as PropType<Attachment[]>,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Attachments'
    }
  },
  emits: ['download-start', 'download-complete', 'download-error'],
  setup(props, { emit }) {
    const downloading = ref(false);
    const downloadingItems = ref(new Set<number>());

    const getFileIcon = (filename: string): string => {
      return fileDownloadService.getFileIcon(filename);
    };

    const handleDownload = async (attachment: Attachment) => {
      if (!attachment) return;

      emit('download-start', attachment);
      
      if (props.multiple) {
        downloadingItems.value.add(attachment.id);
      } else {
        downloading.value = true;
      }

      try {
        const result = await fileDownloadService.downloadFile(attachment);
        
        if (result.success) {
          emit('download-complete', attachment);
        } else {
          emit('download-error', { attachment, error: result.error });
        }
      } catch (error) {
        emit('download-error', { attachment, error });
      } finally {
        if (props.multiple) {
          downloadingItems.value.delete(attachment.id);
        } else {
          downloading.value = false;
        }
      }
    };

    const downloadAll = async () => {
      if (!props.attachments || props.attachments.length === 0) return;

      downloading.value = true;
      
      try {
        await fileDownloadService.downloadMultipleFiles(props.attachments);
        emit('download-complete', props.attachments);
      } catch (error) {
        emit('download-error', { attachments: props.attachments, error });
      } finally {
        downloading.value = false;
      }
    };

    return {
      downloading,
      downloadingItems,
      getFileIcon,
      handleDownload,
      downloadAll
    };
  }
});
</script>

<style scoped>
.attachment-download {
  width: 100%;
}

.attachment-item {
  margin-bottom: 8px;
  transition: opacity 0.3s ease;
}

.attachment-item.downloading {
  opacity: 0.7;
}

.attachment-btn {
  width: 100%;
  justify-content: flex-start;
  text-transform: none;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.attachment-btn:hover {
  background-color: #f5f5f5;
}

.attachment-name {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.attachments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.attachments-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.no-attachments {
  display: flex;
  align-items: center;
  color: #666;
  font-style: italic;
  padding: 16px;
  text-align: center;
  border: 1px dashed #ccc;
  border-radius: 4px;
}

.rotating {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .attachment-name {
    max-width: 150px;
  }
  
  .attachments-header {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}
</style>
