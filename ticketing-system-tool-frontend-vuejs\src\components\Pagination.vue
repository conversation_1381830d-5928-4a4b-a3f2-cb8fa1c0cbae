<template>
  <div class="pagination">
    <button
      :disabled="currentPage === 1"
      @click="changePage(1)"
    >
      First
    </button>
    <button
      :disabled="currentPage === 1"
      @click="changePage(currentPage - 1)"
    >
      Previous
    </button>

    <span>Page {{ currentPage }} of {{ totalPages }}</span>

    <button
      :disabled="currentPage === totalPages"
      @click="changePage(currentPage + 1)"
    >
      Next
    </button>
    <button
      :disabled="currentPage === totalPages"
      @click="changePage(totalPages)"
    >
      Last
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  props: {
    currentPage: {
      type: Number,
      required: true,
    },
    totalPages: {
      type: Number,
      required: true,
    },
  },
  methods: {
    changePage(page: number) {
      if (page >= 1 && page <= this.totalPages) {
        this.$emit("page-changed", page);
      }
    },
  },
});
</script>

<style scoped>
.pagination {
  display: flex;
  align-items: center;
  gap: 10px;
  /* margin-top: 20px; */
}
button {
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  cursor: pointer;
}
button:disabled {
  background: gray;
  cursor: not-allowed;
}
</style>
