image: node:22  # Use Node.js 18 image

stages:
  - build
  - test
  - deploy

# Build job
build-job:
  stage: build
  script:
    - npm cache clean --force
    - echo "Installing dependencies..."
    - npm install
    - echo "Building Vue.js project..."
    - npm run build
  artifacts:
    paths:
      - dist/  # Store the build files
  only:
    - develop  # Run only on main branch

# Linting and Unit Tests
#lint-test-job:
  #stage: test
  #script:
    #- echo "Running ESLint..."
    #- npm run lint
    #- echo "Running Unit Tests..."
    #- npm run test
  #only:
    #- develop

# Deploy Job (Using Jenkins)
deploy-job:
  stage: deploy
  script:
    - echo "Triggering Jenkins job..."
    - curl -X POST "http://jenkins.nexware-global.com:8060/job/vue-deploy/build?token=cc849d309aa2b201c44c31ae1e2eef83"
  only:
    - develop

